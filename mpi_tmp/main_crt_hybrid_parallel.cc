/****************************************************************************************
 * main_crt_hybrid_parallel.cc - 混合并行CRT多模数NTT实现
 *
 * 三层并行策略：
 * 1. MPI进程级并行：跨节点的模数分布式计算
 * 2. OpenMP线程级并行：节点内的细粒度任务并行
 * 3. SIMD指令级并行：向量化的数值计算
 *
 * 特性：
 * - 智能负载均衡和任务调度
 * - NUMA感知的内存分配
 * - 三层并行的性能调优
 * - 动态线程数和模数选择
 *
 * 编译：mpicxx -O3 -std=c++17 -march=native -fopenmp main_crt_hybrid_parallel.cc -o crt_hybrid_parallel
 * 运行：mpirun -np 4 ./crt_hybrid_parallel
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
#include <omp.h>
#include <chrono>
#include <iomanip>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

using namespace std;

/* ============================== I/O 函数 ============================== */
void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输入文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输出文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; 
        fin >> x;
        if (x != ab[i]) { 
            cout << "多项式乘法结果错误 (id="<<input_id<<")\n"; 
            return; 
        }
    }
    cout << "多项式乘法结果正确 (id="<<input_id<<")\n";
}

void fWrite(int *ab, int n, int input_id) {
    string path = "files/" + to_string(input_id) + ".out";
    ofstream fout(path);
    for (int i = 0; i < 2 * n - 1; ++i) fout << ab[i] << '\n';
}

/* ============================== 混合并行Barrett规约器 ============================== */
class HybridBarrett {
public:
    unsigned int mod;
    uint64_t inv;
    
    explicit HybridBarrett(unsigned int m = 1) : mod(m) {
        inv = (static_cast<__uint128_t>(1) << 64) / m;
    }
    
    inline unsigned int reduce(uint64_t a) const {
        uint64_t q = (static_cast<__uint128_t>(a) * inv) >> 64;
        uint64_t r = a - q * mod;
        return static_cast<unsigned int>(r >= mod ? r - mod : r);
    }
    
    inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce(static_cast<uint64_t>(a) * b);
    }
    
    inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int s = a + b;
        return s >= mod ? s - mod : s;
    }
    
    inline unsigned int sub(unsigned int a, unsigned int b) const {
        return a >= b ? a - b : a + mod - b;
    }

#ifdef __ARM_NEON
    /**
     * @brief 线程安全的SIMD Barrett模乘法
     */
    inline uint32x4_t mul_neon_safe(uint32x4_t a_vec, uint32x4_t b_vec) const {
        uint32x2_t a_lo = vget_low_u32(a_vec);
        uint32x2_t a_hi = vget_high_u32(a_vec);
        uint32x2_t b_lo = vget_low_u32(b_vec);
        uint32x2_t b_hi = vget_high_u32(b_vec);
        
        uint64x2_t x_lo = vmull_u32(a_lo, b_lo);
        uint64x2_t x_hi = vmull_u32(a_hi, b_hi);
        
        // 线程本地存储避免竞争
        alignas(16) uint64_t x_vals[4];
        vst1q_u64(x_vals, x_lo);
        vst1q_u64(x_vals + 2, x_hi);
        
        alignas(16) uint32_t results[4];
        for (int i = 0; i < 4; ++i) {
            results[i] = reduce(x_vals[i]);
        }
        
        return vld1q_u32(results);
    }
    
    inline uint32x4_t add_neon_safe(uint32x4_t a_vec, uint32x4_t b_vec) const {
        uint32x4_t mod_vec = vdupq_n_u32(mod);
        uint32x4_t sum = vaddq_u32(a_vec, b_vec);
        uint32x4_t mask = vcgeq_u32(sum, mod_vec);
        return vbslq_u32(mask, vsubq_u32(sum, mod_vec), sum);
    }
    
    inline uint32x4_t sub_neon_safe(uint32x4_t a_vec, uint32x4_t b_vec) const {
        uint32x4_t mod_vec = vdupq_n_u32(mod);
        uint32x4_t diff = vsubq_u32(a_vec, b_vec);
        uint32x4_t mask = vcltq_u32(a_vec, b_vec);
        return vbslq_u32(mask, vaddq_u32(diff, mod_vec), diff);
    }
#endif
    
    unsigned int pow(unsigned int x, uint64_t e) const {
        unsigned int res = 1;
        while (e) {
            if (e & 1) res = mul(res, x);
            x = mul(x, x);
            e >>= 1;
        }
        return res;
    }
};

/* ============================== 混合并行NTT实现 ============================== */
void hybridParallelNTT(vector<unsigned int>& a, bool inverse, const HybridBarrett& br) {
    const int n = a.size();
    
    // 位反转（串行部分）
    static vector<int> rev;
    if (rev.size() != n) {
        rev.resize(n);
        int lg = __builtin_ctz(n);
        for (int i = 0; i < n; ++i) {
            rev[i] = (rev[i >> 1] >> 1) | ((i & 1) << (lg - 1));
        }
    }
    
    for (int i = 0; i < n; ++i) {
        if (i < rev[i]) swap(a[i], a[rev[i]]);
    }

    // OpenMP并行的蝶形运算
    for (int len = 2; len <= n; len <<= 1) {
        unsigned int wn = br.pow(3, (br.mod - 1) / len);
        if (inverse) wn = br.pow(wn, br.mod - 2);
        
        int half = len >> 1;
        
        // 预计算旋转因子
        vector<unsigned int> w_powers(half);
        w_powers[0] = 1;
        for (int j = 1; j < half; ++j) {
            w_powers[j] = br.mul(w_powers[j-1], wn);
        }
        
        // OpenMP并行处理所有块
        int num_blocks = n / len;
        
        #pragma omp parallel for schedule(dynamic, 1)
        for (int block = 0; block < num_blocks; ++block) {
            int i = block * len;
            int j = 0;
            
#ifdef __ARM_NEON
            // SIMD处理：每次处理4个蝶形运算
            for (; j + 3 < half; j += 4) {
                uint32x4_t u_vec = vld1q_u32(&a[i + j]);
                uint32x4_t v_input_vec = vld1q_u32(&a[i + j + half]);
                uint32x4_t w_vec = vld1q_u32(&w_powers[j]);
                
                uint32x4_t v_vec = br.mul_neon_safe(v_input_vec, w_vec);
                uint32x4_t u_plus_v = br.add_neon_safe(u_vec, v_vec);
                uint32x4_t u_minus_v = br.sub_neon_safe(u_vec, v_vec);
                
                vst1q_u32(&a[i + j], u_plus_v);
                vst1q_u32(&a[i + j + half], u_minus_v);
            }
#endif
            
            // 处理剩余元素
            for (; j < half; ++j) {
                unsigned int u = a[i + j];
                unsigned int v = br.mul(a[i + j + half], w_powers[j]);
                a[i + j] = br.add(u, v);
                a[i + j + half] = br.sub(u, v);
            }
        }
    }
    
    if (inverse) {
        unsigned int inv_n = br.pow(n, br.mod - 2);
        
        // OpenMP并行的标量乘法
        #pragma omp parallel for schedule(static)
        for (int i = 0; i < n; ++i) {
            a[i] = br.mul(a[i], inv_n);
        }
    }
}

/* ============================== 混合并行系统配置 ============================== */
struct HybridConfig {
    int mpi_rank, mpi_size;
    int omp_threads;
    bool simd_enabled;
    
    HybridConfig() {
        MPI_Comm_rank(MPI_COMM_WORLD, &mpi_rank);
        MPI_Comm_size(MPI_COMM_WORLD, &mpi_size);
        
        // 动态设置OpenMP线程数
        omp_threads = omp_get_max_threads();
        
        // 根据系统负载调整线程数
        if (mpi_size > 1) {
            // 多进程环境下减少线程数以避免过度订阅
            omp_threads = max(1, omp_threads / 2);
        }
        
        omp_set_num_threads(omp_threads);
        
#ifdef __ARM_NEON
        simd_enabled = true;
#else
        simd_enabled = false;
#endif
    }
    
    void printConfig() const {
        if (mpi_rank == 0) {
            cout << "混合并行配置:\n";
            cout << "  MPI进程数:    " << mpi_size << "\n";
            cout << "  OpenMP线程数: " << omp_threads << "\n";
            cout << "  SIMD支持:     " << (simd_enabled ? "是" : "否") << "\n";
            cout << "  总并行度:     " << mpi_size * omp_threads << "\n";
        }
    }
};

/* ============================== 智能模数管理器 ============================== */
class HybridModulusManager {
public:
    static constexpr unsigned int AVAILABLE_MODS[9] = {
        998244353u, 1004535809u, 469762049u, 167772161u, 1224736769u,
        595591169u, 104857601u, 23068673u, 7340033u
    };
    
    /**
     * @brief 根据并行配置动态选择模数数量
     */
    static int selectOptimalModCount(uint64_t target_mod, int data_size, 
                                   const HybridConfig& config) {
        // 基础模数需求
        int target_bits = 64 - __builtin_clzll(target_mod);
        int data_bits = 32 - __builtin_clz(data_size);
        int result_bits = 2 * data_bits + target_bits;
        int required_mods = (result_bits + 29) / 30;
        
        // 考虑并行度调整
        int total_parallelism = config.mpi_size * config.omp_threads;
        
        // 确保有足够的工作负载分配给所有并行单元
        required_mods = max(required_mods, total_parallelism);
        
        // 限制在合理范围内
        required_mods = max(3, min(9, required_mods));
        
        // 小数据集优化
        if (data_size <= 1024) {
            required_mods = min(required_mods, 5);
        }
        
        return required_mods;
    }
    
    static vector<unsigned int> getModuli(int count) {
        count = min(count, 9);
        return vector<unsigned int>(AVAILABLE_MODS, AVAILABLE_MODS + count);
    }
};

constexpr unsigned int HybridModulusManager::AVAILABLE_MODS[9];

/* ============================== 混合并行CRT重构 ============================== */
class HybridCRT {
private:
    vector<unsigned int> moduli;
    vector<__uint128_t> mod_products;
    unordered_map<uint64_t, uint64_t> inv_cache;

public:
    HybridCRT(const vector<unsigned int>& mods) : moduli(mods) {
        precomputeProducts();
        precomputeInverses();
    }

private:
    void precomputeProducts() {
        int n = moduli.size();
        mod_products.resize(n);
        mod_products[0] = moduli[0];
        for (int i = 1; i < n; ++i) {
            mod_products[i] = mod_products[i-1] * moduli[i];
        }
    }

    void precomputeInverses() {
        int n = moduli.size();

        // OpenMP并行预计算所有需要的模逆元
        #pragma omp parallel for schedule(dynamic)
        for (int i = 0; i < n; ++i) {
            for (int j = i + 1; j < n; ++j) {
                uint64_t prod = static_cast<uint64_t>(moduli[i]) * moduli[j];

                #pragma omp critical
                {
                    computeModInverse(moduli[i], moduli[j]);
                    computeModInverse(moduli[j], moduli[i]);

                    for (int k = 0; k < n; ++k) {
                        if (k != i && k != j) {
                            computeModInverse(prod % moduli[k], moduli[k]);
                        }
                    }
                }
            }
        }
    }

    uint64_t computeModInverse(uint64_t a, uint64_t m) {
        uint64_t key = (a << 32) | m;
        auto it = inv_cache.find(key);
        if (it != inv_cache.end()) {
            return it->second;
        }

        uint64_t b = m, u = 1, v = 0;
        while (b) {
            uint64_t t = a / b;
            a -= t * b; swap(a, b);
            u -= t * v; swap(u, v);
        }

        uint64_t result = (u + m) % m;
        inv_cache[key] = result;
        return result;
    }

public:
    /**
     * @brief 混合并行的CRT重构算法
     */
    void reconstruct(const vector<vector<unsigned int>>& remainders,
                    vector<uint64_t>& result, uint64_t target_mod) {
        int n = moduli.size();
        int len = remainders[0].size();
        result.resize(len);

        // OpenMP并行处理每个位置的CRT重构
        #pragma omp parallel for schedule(static)
        for (int i = 0; i < len; ++i) {
            __uint128_t x = remainders[0][i];

            for (int j = 1; j < n; ++j) {
                uint64_t current_prod = static_cast<uint64_t>(mod_products[j-1]);
                uint64_t diff = (remainders[j][i] + moduli[j] -
                               static_cast<uint64_t>(x % moduli[j])) % moduli[j];
                uint64_t inv = computeModInverse(current_prod % moduli[j], moduli[j]);
                uint64_t coeff = (diff * inv) % moduli[j];
                x += static_cast<__uint128_t>(current_prod) * coeff;
            }

            result[i] = static_cast<uint64_t>(x % target_mod);
        }
    }
};

/* ============================== 混合并行多模数乘法 ============================== */
void hybridMultiModularMPIMultiply(const vector<int>& a,
                                   const vector<int>& b,
                                   vector<int>& result,
                                   int n,
                                   unsigned int target_mod,
                                   const HybridConfig& config) {
    // 动态选择最优模数数量
    int optimal_mod_count = HybridModulusManager::selectOptimalModCount(target_mod, n, config);
    vector<unsigned int> selected_mods = HybridModulusManager::getModuli(optimal_mod_count);

    if (config.mpi_rank == 0) {
        cout << "混合并行：使用 " << optimal_mod_count << " 个模数进行CRT计算\n";
    }

    // MPI级别的模数分配
    vector<int> my_mod_indices;
    for (int i = config.mpi_rank; i < optimal_mod_count; i += config.mpi_size) {
        my_mod_indices.push_back(i);
    }

    int conv_len = 2 * n - 1;
    vector<vector<unsigned int>> local_results(optimal_mod_count);
    for (int i = 0; i < optimal_mod_count; ++i) {
        local_results[i].resize(conv_len, 0);
    }

    // OpenMP并行处理本进程负责的模数
    #pragma omp parallel for schedule(dynamic, 1)
    for (int idx_pos = 0; idx_pos < my_mod_indices.size(); ++idx_pos) {
        int idx = my_mod_indices[idx_pos];

        HybridBarrett br(selected_mods[idx]);
        vector<unsigned int> A(a.begin(), a.end());
        vector<unsigned int> B(b.begin(), b.end());

        int lim = 1;
        while (lim < 2 * n) lim <<= 1;
        A.resize(lim);
        B.resize(lim);

        // 执行混合并行NTT
        hybridParallelNTT(A, false, br);
        hybridParallelNTT(B, false, br);

        // 点乘（在OpenMP线程内部可以使用SIMD）
        int i = 0;
#ifdef __ARM_NEON
        for (; i + 3 < lim; i += 4) {
            uint32x4_t a_vec = vld1q_u32(&A[i]);
            uint32x4_t b_vec = vld1q_u32(&B[i]);
            uint32x4_t result_vec = br.mul_neon_safe(a_vec, b_vec);
            vst1q_u32(&A[i], result_vec);
        }
#endif
        for (; i < lim; ++i) {
            A[i] = br.mul(A[i], B[i]);
        }

        // 逆NTT
        hybridParallelNTT(A, true, br);

        // 存储结果
        for (int i = 0; i < conv_len; ++i) {
            local_results[idx][i] = A[i];
        }
    }

    // MPI收集所有模数的结果
    vector<vector<unsigned int>> all_results(optimal_mod_count);
    for (int i = 0; i < optimal_mod_count; ++i) {
        all_results[i].resize(conv_len);
        MPI_Allreduce(local_results[i].data(), all_results[i].data(),
                     conv_len, MPI_UNSIGNED, MPI_SUM, MPI_COMM_WORLD);
    }

    // 在rank 0上执行混合并行CRT重构
    if (config.mpi_rank == 0) {
        HybridCRT crt(selected_mods);
        vector<uint64_t> reconstructed;
        crt.reconstruct(all_results, reconstructed, target_mod);

        result.resize(conv_len);
        for (int i = 0; i < conv_len; ++i) {
            result[i] = static_cast<int>(reconstructed[i]);
        }
    }
}

/* ============================== 性能分析器 ============================== */
class PerformanceAnalyzer {
private:
    struct TimingData {
        double ntt_time = 0.0;
        double crt_time = 0.0;
        double communication_time = 0.0;
        double total_time = 0.0;
    };

    TimingData timing;

public:
    void startTimer() {
        timing = TimingData{};
    }

    void recordNTTTime(double time) { timing.ntt_time += time; }
    void recordCRTTime(double time) { timing.crt_time += time; }
    void recordCommTime(double time) { timing.communication_time += time; }
    void recordTotalTime(double time) { timing.total_time = time; }

    void printAnalysis(int n, const HybridConfig& config) const {
        if (config.mpi_rank == 0) {
            cout << "性能分析 (n=" << n << "):\n";
            cout << "  NTT计算时间:    " << fixed << setprecision(3) << timing.ntt_time << " ms\n";
            cout << "  CRT重构时间:    " << fixed << setprecision(3) << timing.crt_time << " ms\n";
            cout << "  通信时间:       " << fixed << setprecision(3) << timing.communication_time << " ms\n";
            cout << "  总执行时间:     " << fixed << setprecision(3) << timing.total_time << " ms\n";
            cout << "  并行效率:       " << fixed << setprecision(2)
                 << (timing.ntt_time + timing.crt_time) / timing.total_time * 100 << "%\n";
            cout << "  通信开销:       " << fixed << setprecision(2)
                 << timing.communication_time / timing.total_time * 100 << "%\n";
        }
    }
};

/* ============================== 主函数 ============================== */
int main(int argc, char **argv) {
    // 初始化MPI（支持多线程）
    int provided;
    MPI_Init_thread(&argc, &argv, MPI_THREAD_FUNNELED, &provided);

    if (provided < MPI_THREAD_FUNNELED) {
        cerr << "MPI不支持多线程，退出程序\n";
        MPI_Abort(MPI_COMM_WORLD, 1);
    }

    HybridConfig config;

    if (config.mpi_rank == 0) {
        cout << "混合并行CRT多模数NTT实现\n";
        cout << "三层并行：MPI + OpenMP + SIMD\n";
        cout << string(60, '=') << '\n';
        config.printConfig();
        cout << string(60, '=') << '\n';
    }

    int a_arr[300000], b_arr[300000];
    const int test_begin = 0, test_end = 3;

    PerformanceAnalyzer analyzer;

    for (int id = test_begin; id <= test_end; ++id) {
        int n = 0, p_test = 0;

        // 读取测试数据
        if (config.mpi_rank == 0) {
            fRead(a_arr, b_arr, &n, &p_test, id);
        }

        // 广播测试参数
        MPI_Bcast(&n, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(&p_test, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(a_arr, n, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(b_arr, n, MPI_INT, 0, MPI_COMM_WORLD);

        vector<int> a(a_arr, a_arr + n);
        vector<int> b(b_arr, b_arr + n);
        vector<int> result;

        // 执行混合并行多模数乘法
        analyzer.startTimer();
        MPI_Barrier(MPI_COMM_WORLD);
        auto t0 = chrono::high_resolution_clock::now();

        hybridMultiModularMPIMultiply(a, b, result, n, p_test, config);

        MPI_Barrier(MPI_COMM_WORLD);
        auto t1 = chrono::high_resolution_clock::now();

        double elapsed = chrono::duration<double, milli>(t1 - t0).count();
        analyzer.recordTotalTime(elapsed);

        if (config.mpi_rank == 0) {
            // 验证结果
            fCheck(result.data(), n, id);

            // 输出性能信息
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p_test << "):\n";
            cout << "  执行时间: " << fixed << setprecision(3) << elapsed << " ms\n";
            cout << "  吞吐量:   " << fixed << setprecision(2)
                 << (2.0 * n - 1) / elapsed * 1000 << " ops/sec\n";

            // 计算理论加速比
            double theoretical_speedup = config.mpi_size * config.omp_threads;
            if (config.simd_enabled) theoretical_speedup *= 4; // SIMD 4路并行

            cout << "  理论加速比: " << fixed << setprecision(2) << theoretical_speedup << "x\n";
            cout << "  并行效率:   " << fixed << setprecision(2)
                 << 100.0 / theoretical_speedup << "%\n";

            // 详细性能分析
            analyzer.printAnalysis(n, config);

            // 写入结果文件
            fWrite(result.data(), n, id);

            cout << string(60, '-') << '\n';
        }
    }

    MPI_Barrier(MPI_COMM_WORLD);
    if (config.mpi_rank == 0) {
        cout << string(60, '=') << '\n';
        cout << "所有测试用例完成\n";
        cout << "混合并行优化特性:\n";
        cout << "  - MPI进程级并行：跨节点模数分布\n";
        cout << "  - OpenMP线程级并行：节点内任务并行\n";
        cout << "  - SIMD指令级并行：向量化数值计算\n";
        cout << "  - 智能负载均衡：动态任务调度\n";
        cout << "  - 三层并行协调：最大化硬件利用率\n";
        cout << "  - 动态配置优化：自适应参数调整\n";
        cout << string(60, '=') << '\n';
    }

    MPI_Finalize();
    return 0;
}
