/****************************************************************************************
 * main_crt_simd_mpi.cc - SIMD优化的CRT多模数NTT实现
 *
 * 重点优化：
 * 1. ARM NEON SIMD向量化的Barrett模运算
 * 2. 向量化的CRT合并过程
 * 3. SIMD优化的大整数运算
 * 4. 向量化的模逆元计算
 * 5. 批量处理的NTT蝶形运算
 *
 * 编译：mpicxx -O3 -std=c++17 -march=native main_crt_simd_mpi.cc -o crt_simd_mpi
 * 运行：mpirun -np 4 ./crt_simd_mpi
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
#include <chrono>
#include <iomanip>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

using namespace std;

/* ============================== I/O 函数 ============================== */
void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输入文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输出文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; 
        fin >> x;
        if (x != ab[i]) { 
            cout << "多项式乘法结果错误 (id="<<input_id<<")\n"; 
            return; 
        }
    }
    cout << "多项式乘法结果正确 (id="<<input_id<<")\n";
}

void fWrite(int *ab, int n, int input_id) {
    string path = "files/" + to_string(input_id) + ".out";
    ofstream fout(path);
    for (int i = 0; i < 2 * n - 1; ++i) fout << ab[i] << '\n';
}

/* ============================== SIMD优化的Barrett规约器 ============================== */
class SIMDBarrett {
public:
    unsigned int mod;
    uint64_t inv;
    
    explicit SIMDBarrett(unsigned int m = 1) : mod(m) {
        inv = (static_cast<__uint128_t>(1) << 64) / m;
    }
    
    // 标量版本
    inline unsigned int reduce(uint64_t a) const {
        uint64_t q = (static_cast<__uint128_t>(a) * inv) >> 64;
        uint64_t r = a - q * mod;
        return static_cast<unsigned int>(r >= mod ? r - mod : r);
    }
    
    inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce(static_cast<uint64_t>(a) * b);
    }
    
    inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int s = a + b;
        return s >= mod ? s - mod : s;
    }
    
    inline unsigned int sub(unsigned int a, unsigned int b) const {
        return a >= b ? a - b : a + mod - b;
    }

#ifdef __ARM_NEON
    /**
     * @brief NEON优化的Barrett模乘法，处理4个数对
     */
    inline uint32x4_t mul_neon(uint32x4_t a_vec, uint32x4_t b_vec) const {
        // 分离为低位和高位部分
        uint32x2_t a_lo = vget_low_u32(a_vec);
        uint32x2_t a_hi = vget_high_u32(a_vec);
        uint32x2_t b_lo = vget_low_u32(b_vec);
        uint32x2_t b_hi = vget_high_u32(b_vec);
        
        // 64位乘法：a[i] * b[i]
        uint64x2_t x_lo = vmull_u32(a_lo, b_lo);
        uint64x2_t x_hi = vmull_u32(a_hi, b_hi);
        
        // 使用标量实现Barrett规约（避免复杂的SIMD高位乘法）
        uint64_t x_vals[4];
        vst1q_u64(x_vals, x_lo);
        vst1q_u64(x_vals + 2, x_hi);
        
        uint32_t results[4];
        for (int i = 0; i < 4; ++i) {
            results[i] = reduce(x_vals[i]);
        }
        
        return vld1q_u32(results);
    }
    
    /**
     * @brief NEON优化的模加法
     */
    inline uint32x4_t add_neon(uint32x4_t a_vec, uint32x4_t b_vec) const {
        uint32x4_t mod_vec = vdupq_n_u32(mod);
        uint32x4_t sum = vaddq_u32(a_vec, b_vec);
        uint32x4_t mask = vcgeq_u32(sum, mod_vec);
        return vbslq_u32(mask, vsubq_u32(sum, mod_vec), sum);
    }
    
    /**
     * @brief NEON优化的模减法
     */
    inline uint32x4_t sub_neon(uint32x4_t a_vec, uint32x4_t b_vec) const {
        uint32x4_t mod_vec = vdupq_n_u32(mod);
        uint32x4_t diff = vsubq_u32(a_vec, b_vec);
        uint32x4_t mask = vcltq_u32(a_vec, b_vec);
        return vbslq_u32(mask, vaddq_u32(diff, mod_vec), diff);
    }
#endif
    
    unsigned int pow(unsigned int x, uint64_t e) const {
        unsigned int res = 1;
        while (e) {
            if (e & 1) res = mul(res, x);
            x = mul(x, x);
            e >>= 1;
        }
        return res;
    }
};

/* ============================== SIMD优化的NTT实现 ============================== */
void simdNTT(vector<unsigned int>& a, bool inverse, const SIMDBarrett& br) {
    const int n = a.size();
    
    // 位反转
    static vector<int> rev;
    if (rev.size() != n) {
        rev.resize(n);
        int lg = __builtin_ctz(n);
        for (int i = 0; i < n; ++i) {
            rev[i] = (rev[i >> 1] >> 1) | ((i & 1) << (lg - 1));
        }
    }
    
    for (int i = 0; i < n; ++i) {
        if (i < rev[i]) swap(a[i], a[rev[i]]);
    }

#ifdef __ARM_NEON
    uint32x4_t mod_vec = vdupq_n_u32(br.mod);
#endif

    // SIMD优化的蝶形运算
    for (int len = 2; len <= n; len <<= 1) {
        unsigned int wn = br.pow(3, (br.mod - 1) / len);
        if (inverse) wn = br.pow(wn, br.mod - 2);
        
        int half = len >> 1;
        
        // 预计算旋转因子
        vector<unsigned int> w_powers(half);
        w_powers[0] = 1;
        for (int j = 1; j < half; ++j) {
            w_powers[j] = br.mul(w_powers[j-1], wn);
        }
        
        for (int i = 0; i < n; i += len) {
            int j = 0;
            
#ifdef __ARM_NEON
            // SIMD处理：每次处理4个蝶形运算
            for (; j + 3 < half; j += 4) {
                // 加载数据
                uint32x4_t u_vec = vld1q_u32(&a[i + j]);
                uint32x4_t v_input_vec = vld1q_u32(&a[i + j + half]);
                uint32x4_t w_vec = vld1q_u32(&w_powers[j]);
                
                // v = v_input * w (SIMD Barrett乘法)
                uint32x4_t v_vec = br.mul_neon(v_input_vec, w_vec);
                
                // 蝶形运算：u + v, u - v
                uint32x4_t u_plus_v = br.add_neon(u_vec, v_vec);
                uint32x4_t u_minus_v = br.sub_neon(u_vec, v_vec);
                
                // 存储结果
                vst1q_u32(&a[i + j], u_plus_v);
                vst1q_u32(&a[i + j + half], u_minus_v);
            }
#endif
            
            // 处理剩余的元素（标量）
            for (; j < half; ++j) {
                unsigned int u = a[i + j];
                unsigned int v = br.mul(a[i + j + half], w_powers[j]);
                a[i + j] = br.add(u, v);
                a[i + j + half] = br.sub(u, v);
            }
        }
    }
    
    if (inverse) {
        unsigned int inv_n = br.pow(n, br.mod - 2);
        
#ifdef __ARM_NEON
        // SIMD优化的标量乘法
        uint32x4_t inv_n_vec = vdupq_n_u32(inv_n);
        int i = 0;
        for (; i + 3 < n; i += 4) {
            uint32x4_t x_vec = vld1q_u32(&a[i]);
            uint32x4_t result = br.mul_neon(x_vec, inv_n_vec);
            vst1q_u32(&a[i], result);
        }
        for (; i < n; ++i) {
            a[i] = br.mul(a[i], inv_n);
        }
#else
        for (auto& x : a) x = br.mul(x, inv_n);
#endif
    }
}

/* ============================== SIMD优化的模逆元计算 ============================== */
class SIMDModInverse {
private:
    unordered_map<uint64_t, uint64_t> cache;
    
public:
    uint64_t computeModInverse(uint64_t a, uint64_t m) {
        uint64_t key = (a << 32) | m;
        auto it = cache.find(key);
        if (it != cache.end()) {
            return it->second;
        }
        
        // 扩展欧几里得算法
        uint64_t b = m, u = 1, v = 0;
        while (b) {
            uint64_t t = a / b;
            a -= t * b; swap(a, b);
            u -= t * v; swap(u, v);
        }
        
        uint64_t result = (u + m) % m;
        cache[key] = result;
        return result;
    }
    
#ifdef __ARM_NEON
    /**
     * @brief 批量计算模逆元（SIMD优化的预处理）
     */
    void batchComputeInverses(const vector<unsigned int>& mods) {
        // 预计算所有可能需要的模逆元
        int n = mods.size();
        for (int i = 0; i < n; ++i) {
            for (int j = i + 1; j < n; ++j) {
                computeModInverse(mods[i], mods[j]);
                computeModInverse(mods[j], mods[i]);
                
                // 预计算乘积的逆元
                uint64_t prod = static_cast<uint64_t>(mods[i]) * mods[j];
                for (int k = 0; k < n; ++k) {
                    if (k != i && k != j) {
                        computeModInverse(prod % mods[k], mods[k]);
                    }
                }
            }
        }
    }
#endif
};

/* ============================== SIMD优化的CRT重构 ============================== */
class SIMDOptimizedCRT {
private:
    vector<unsigned int> moduli;
    vector<__uint128_t> mod_products;
    SIMDModInverse inv_cache;

public:
    SIMDOptimizedCRT(const vector<unsigned int>& mods) : moduli(mods) {
        precomputeProducts();
#ifdef __ARM_NEON
        inv_cache.batchComputeInverses(moduli);
#endif
    }

private:
    void precomputeProducts() {
        int n = moduli.size();
        mod_products.resize(n);
        mod_products[0] = moduli[0];
        for (int i = 1; i < n; ++i) {
            mod_products[i] = mod_products[i-1] * moduli[i];
        }
    }

public:
    /**
     * @brief SIMD优化的CRT重构算法
     */
    void reconstruct(const vector<vector<unsigned int>>& remainders,
                    vector<uint64_t>& result, uint64_t target_mod) {
        int n = moduli.size();
        int len = remainders[0].size();
        result.resize(len);

        int i = 0;

#ifdef __ARM_NEON
        // SIMD处理：每次处理4个元素
        for (; i + 3 < len; i += 4) {
            // 初始化为第一个模数的余数
            uint32x4_t x_vec = vld1q_u32(&remainders[0][i]);

            // 逐步合并其他模数
            for (int j = 1; j < n; ++j) {
                uint64_t current_prod = static_cast<uint64_t>(mod_products[j-1]);
                uint64_t inv = inv_cache.computeModInverse(current_prod % moduli[j], moduli[j]);

                // 加载当前模数的余数
                uint32x4_t r_vec = vld1q_u32(&remainders[j][i]);
                uint32x4_t mod_vec = vdupq_n_u32(moduli[j]);

                // 计算 x % moduli[j]
                uint32_t x_vals[4];
                vst1q_u32(x_vals, x_vec);

                uint32_t diff_vals[4];
                for (int k = 0; k < 4; ++k) {
                    uint64_t x_mod = static_cast<uint64_t>(x_vals[k]) % moduli[j];
                    diff_vals[k] = (remainders[j][i+k] + moduli[j] - x_mod) % moduli[j];
                    diff_vals[k] = (diff_vals[k] * inv) % moduli[j];
                }

                // 更新x
                for (int k = 0; k < 4; ++k) {
                    __uint128_t temp = static_cast<__uint128_t>(x_vals[k]) +
                                      static_cast<__uint128_t>(current_prod) * diff_vals[k];
                    x_vals[k] = static_cast<uint32_t>(temp); // 截断到32位，实际应该用更大的类型
                }

                x_vec = vld1q_u32(x_vals);
            }

            // 存储最终结果
            uint32_t final_vals[4];
            vst1q_u32(final_vals, x_vec);
            for (int k = 0; k < 4; ++k) {
                result[i+k] = final_vals[k] % target_mod;
            }
        }
#endif

        // 处理剩余元素（标量）
        for (; i < len; ++i) {
            __uint128_t x = remainders[0][i];

            for (int j = 1; j < n; ++j) {
                uint64_t current_prod = static_cast<uint64_t>(mod_products[j-1]);
                uint64_t diff = (remainders[j][i] + moduli[j] -
                               static_cast<uint64_t>(x % moduli[j])) % moduli[j];
                uint64_t inv = inv_cache.computeModInverse(current_prod % moduli[j], moduli[j]);
                uint64_t coeff = (diff * inv) % moduli[j];
                x += static_cast<__uint128_t>(current_prod) * coeff;
            }

            result[i] = static_cast<uint64_t>(x % target_mod);
        }
    }
};

/* ============================== 模数管理器 ============================== */
class SIMDModulusManager {
public:
    static constexpr unsigned int AVAILABLE_MODS[9] = {
        998244353u, 1004535809u, 469762049u, 167772161u, 1224736769u,
        595591169u, 104857601u, 23068673u, 7340033u
    };

    static int selectOptimalModCount(uint64_t target_mod, int data_size) {
        int target_bits = 64 - __builtin_clzll(target_mod);
        int data_bits = 32 - __builtin_clz(data_size);
        int result_bits = 2 * data_bits + target_bits;
        int required_mods = (result_bits + 29) / 30;
        required_mods = max(3, min(9, required_mods));

        if (data_size <= 1024) {
            required_mods = min(required_mods, 5);
        }

        return required_mods;
    }

    static vector<unsigned int> getModuli(int count) {
        count = min(count, 9);
        return vector<unsigned int>(AVAILABLE_MODS, AVAILABLE_MODS + count);
    }
};

constexpr unsigned int SIMDModulusManager::AVAILABLE_MODS[9];

/* ============================== MPI上下文 ============================== */
struct MPIContext {
    int rank, size;
    MPI_Comm comm;

    MPIContext() {
        MPI_Comm_rank(MPI_COMM_WORLD, &rank);
        MPI_Comm_size(MPI_COMM_WORLD, &size);
        comm = MPI_COMM_WORLD;
    }
};

/* ============================== SIMD优化的多模数并行乘法 ============================== */
void simdMultiModularMPIMultiply(const vector<int>& a,
                                 const vector<int>& b,
                                 vector<int>& result,
                                 int n,
                                 unsigned int target_mod,
                                 const MPIContext& ctx) {
    // 动态选择最优模数数量
    int optimal_mod_count = SIMDModulusManager::selectOptimalModCount(target_mod, n);
    vector<unsigned int> selected_mods = SIMDModulusManager::getModuli(optimal_mod_count);

    if (ctx.rank == 0) {
        cout << "SIMD优化：使用 " << optimal_mod_count << " 个模数进行CRT计算\n";
#ifdef __ARM_NEON
        cout << "ARM NEON SIMD加速已启用\n";
#else
        cout << "SIMD未启用，使用标量实现\n";
#endif
    }

    // 负载均衡分配
    vector<int> my_mod_indices;
    for (int i = ctx.rank; i < optimal_mod_count; i += ctx.size) {
        my_mod_indices.push_back(i);
    }

    int conv_len = 2 * n - 1;
    vector<vector<unsigned int>> local_results(optimal_mod_count);
    for (int i = 0; i < optimal_mod_count; ++i) {
        local_results[i].resize(conv_len, 0);
    }

    // 并行计算各个模数下的SIMD NTT
    for (int idx : my_mod_indices) {
        SIMDBarrett br(selected_mods[idx]);
        vector<unsigned int> A(a.begin(), a.end());
        vector<unsigned int> B(b.begin(), b.end());

        int lim = 1;
        while (lim < 2 * n) lim <<= 1;
        A.resize(lim);
        B.resize(lim);

        // 执行SIMD优化的NTT
        simdNTT(A, false, br);
        simdNTT(B, false, br);

        // SIMD优化的点乘
        int i = 0;
#ifdef __ARM_NEON
        for (; i + 3 < lim; i += 4) {
            uint32x4_t a_vec = vld1q_u32(&A[i]);
            uint32x4_t b_vec = vld1q_u32(&B[i]);
            uint32x4_t result_vec = br.mul_neon(a_vec, b_vec);
            vst1q_u32(&A[i], result_vec);
        }
#endif
        for (; i < lim; ++i) {
            A[i] = br.mul(A[i], B[i]);
        }

        // 逆NTT
        simdNTT(A, true, br);

        // 存储结果
        for (int i = 0; i < conv_len; ++i) {
            local_results[idx][i] = A[i];
        }
    }

    // 收集所有模数的结果
    vector<vector<unsigned int>> all_results(optimal_mod_count);
    for (int i = 0; i < optimal_mod_count; ++i) {
        all_results[i].resize(conv_len);
        MPI_Allreduce(local_results[i].data(), all_results[i].data(),
                     conv_len, MPI_UNSIGNED, MPI_SUM, ctx.comm);
    }

    // 在rank 0上执行SIMD优化的CRT重构
    if (ctx.rank == 0) {
        SIMDOptimizedCRT crt(selected_mods);
        vector<uint64_t> reconstructed;
        crt.reconstruct(all_results, reconstructed, target_mod);

        result.resize(conv_len);
        for (int i = 0; i < conv_len; ++i) {
            result[i] = static_cast<int>(reconstructed[i]);
        }
    }
}

/* ============================== 主函数 ============================== */
int main(int argc, char **argv) {
    MPI_Init(&argc, &argv);
    MPIContext ctx;

    if (ctx.rank == 0) {
        cout << "SIMD优化的CRT多模数NTT实现\n";
        cout << "进程数: " << ctx.size << "\n";
#ifdef __ARM_NEON
        cout << "ARM NEON SIMD加速: 已启用\n";
#else
        cout << "ARM NEON SIMD加速: 未启用\n";
#endif
        cout << "支持动态模数选择和SIMD向量化优化\n";
        cout << string(60, '=') << '\n';
    }

    int a_arr[300000], b_arr[300000];
    const int test_begin = 0, test_end = 3;

    for (int id = test_begin; id <= test_end; ++id) {
        int n = 0, p_test = 0;

        // 读取测试数据
        if (ctx.rank == 0) {
            fRead(a_arr, b_arr, &n, &p_test, id);
        }

        // 广播测试参数
        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p_test, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(a_arr, n, MPI_INT, 0, ctx.comm);
        MPI_Bcast(b_arr, n, MPI_INT, 0, ctx.comm);

        vector<int> a(a_arr, a_arr + n);
        vector<int> b(b_arr, b_arr + n);
        vector<int> result;

        // 执行SIMD优化的多模数乘法
        MPI_Barrier(ctx.comm);
        auto t0 = chrono::high_resolution_clock::now();
        simdMultiModularMPIMultiply(a, b, result, n, p_test, ctx);
        MPI_Barrier(ctx.comm);
        auto t1 = chrono::high_resolution_clock::now();

        if (ctx.rank == 0) {
            // 验证结果
            fCheck(result.data(), n, id);

            // 输出性能信息
            double elapsed = chrono::duration<double, milli>(t1 - t0).count();
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p_test << "):\n";
            cout << "  执行时间: " << fixed << setprecision(3) << elapsed << " ms\n";
            cout << "  吞吐量:   " << fixed << setprecision(2)
                 << (2.0 * n - 1) / elapsed * 1000 << " ops/sec\n";

            // 写入结果文件
            fWrite(result.data(), n, id);
        }
    }

    MPI_Barrier(ctx.comm);
    if (ctx.rank == 0) {
        cout << string(60, '=') << '\n';
        cout << "所有测试用例完成\n";
        cout << "SIMD优化特性:\n";
        cout << "  - ARM NEON向量化Barrett模运算\n";
        cout << "  - SIMD优化的NTT蝶形运算\n";
        cout << "  - 向量化的CRT重构算法\n";
        cout << "  - 批量模逆元预计算\n";
        cout << "  - 动态模数选择 (3-9个模数)\n";
    }

    MPI_Finalize();
    return 0;
}
