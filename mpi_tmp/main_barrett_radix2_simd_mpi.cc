/****************************************************************************************
 * main_barrett_radix2_simd_mpi.cc - SIMD优化的MPI并行Radix-2 NTT实现
 *
 * 特性：
 * 1. 基于Barrett快速取模的Radix-2 NTT算法
 * 2. MPI多进程并行化（数据级并行）
 * 3. ARM NEON SIMD向量化优化
 * 4. 内存访问模式优化
 * 5. 向量化蝶形运算
 * 6. 与现有测试框架完全兼容
 *
 * 编译：mpicxx -O3 -march=native -std=c++17 main_barrett_radix2_simd_mpi.cc -o ntt_radix2_simd_mpi
 * 运行：mpirun -np 4 ./ntt_radix2_simd_mpi
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
#include <iomanip>
#include <chrono>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

using namespace std;

// 使用标准类型以确保MPI兼容性
using u32  = unsigned int;
using u64  = unsigned long long;
#if defined(_MSC_VER) && !defined(__clang__)
using u128 = unsigned __int128;
#else
using u128 = __uint128_t;
#endif

/* ============================== I/O 函数 ============================== */
void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输入文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输出文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; 
        fin >> x;
        if (x != ab[i]) { 
            cout << "多项式乘法结果错误 (id="<<input_id<<")\n"; 
            return; 
        }
    }
    cout << "多项式乘法结果正确 (id="<<input_id<<")\n";
}

void fWrite(int *ab, int n, int input_id) {
    string path = "files/" + to_string(input_id) + ".out";
    ofstream fout(path);
    for (int i = 0; i < 2 * n - 1; ++i) fout << ab[i] << '\n';
}

/* ============================== Barrett 快速取模 ============================== */
class Barrett {
public:
    explicit Barrett(u32 m): mod(m) {
        inv = (static_cast<u128>(1) << 64) / m;
    }
    
    inline u32 reduce(u64 x) const {
        u64 q = (static_cast<u128>(x) * inv) >> 64;
        u64 r = x - q * mod;
        if (r >= mod) r -= mod;
        return static_cast<u32>(r);
    }
    
    inline u32 mul(u32 a, u32 b) const {
        return reduce(static_cast<u64>(a) * b);
    }
    
    inline u32 add(u32 a, u32 b) const {
        u32 s = a + b;
        return s >= mod ? s - mod : s;
    }
    
    inline u32 sub(u32 a, u32 b) const {
        return a >= b ? a - b : a + mod - b;
    }
    
    const u32 mod;
    u64 inv;  // 公开以便SIMD函数访问
};

/* ============================== 工具函数 ============================== */
static u32 mod_pow(u32 a, u64 e, u32 mod) {
    u64 res = 1, base = a;
    while (e) {
        if (e & 1) res = res * base % mod;
        base = base * base % mod;
        e >>= 1;
    }
    return static_cast<u32>(res);
}

static void bit_reverse(vector<int>& rev, int n) {
    int lg = __builtin_ctz(n);
    rev.resize(n);
    for (int i = 0; i < n; ++i) {
        rev[i] = (rev[i >> 1] >> 1) | ((i & 1) << (lg - 1));
    }
}

/* ============================== MPI 上下文 ============================== */
struct MPIContext {
    int rank, size;
    MPI_Comm comm;
};

/* ============================== SIMD 优化的Barrett模运算 ============================== */
#ifdef __ARM_NEON
/**
 * @brief NEON优化的Barrett模乘法，处理4个u32对
 * @param val_a 输入向量a
 * @param val_b 输入向量b  
 * @param br Barrett对象引用
 * @return 4个(a[i] * b[i]) mod br.mod的结果
 */
inline uint32x4_t barrett_mul_neon(uint32x4_t val_a, uint32x4_t val_b, const Barrett& br) {
    // 分离为低位和高位部分
    uint32x2_t a_lo = vget_low_u32(val_a);
    uint32x2_t a_hi = vget_high_u32(val_a);
    uint32x2_t b_lo = vget_low_u32(val_b);
    uint32x2_t b_hi = vget_high_u32(val_b);
    
    // 64位乘法：a[i] * b[i]
    uint64x2_t x_lo = vmull_u32(a_lo, b_lo);
    uint64x2_t x_hi = vmull_u32(a_hi, b_hi);
    
    #if defined(__aarch64__) && defined(__ARM_FEATURE_CRYPTO)
    // AArch64有高效的64位乘法指令（需要crypto扩展）
    uint64x2_t inv_vec = vdupq_n_u64(br.inv);
    uint64x2_t mod_vec = vdupq_n_u64(br.mod);

    // Barrett规约：q = (x * inv) >> 64
    // 使用标量实现高位乘法，因为vmulh_u64可能不可用
    uint64_t x_vals[4];
    uint64_t inv_val = br.inv, mod_val = br.mod;
    vst1q_u64(x_vals, x_lo);
    vst1q_u64(x_vals + 2, x_hi);

    uint64_t q_vals[4], r_vals[4];
    for(int i = 0; i < 4; ++i) {
        // 手动实现高位乘法
        u128 temp = (u128)x_vals[i] * inv_val;
        q_vals[i] = temp >> 64;
        r_vals[i] = x_vals[i] - q_vals[i] * mod_val;
        if(r_vals[i] >= mod_val) r_vals[i] -= mod_val;
    }

    // 转换回32位
    uint32_t results[4];
    for(int i = 0; i < 4; ++i) {
        results[i] = static_cast<uint32_t>(r_vals[i]);
    }
    return vld1q_u32(results);

    #else
    // 32位ARM或其他平台的标量回退
    uint32_t results[4];
    uint32_t a_vals[4], b_vals[4];
    vst1q_u32(a_vals, val_a);
    vst1q_u32(b_vals, val_b);
    for (int i = 0; i < 4; ++i) {
        results[i] = br.mul(a_vals[i], b_vals[i]);
    }
    return vld1q_u32(results);
    #endif
}

/**
 * @brief NEON优化的模加法
 */
inline uint32x4_t mod_add_neon(uint32x4_t a, uint32x4_t b, uint32x4_t mod_vec) {
    uint32x4_t sum = vaddq_u32(a, b);
    uint32x4_t mask = vcgeq_u32(sum, mod_vec);
    return vbslq_u32(mask, vsubq_u32(sum, mod_vec), sum);
}

/**
 * @brief NEON优化的模减法
 */
inline uint32x4_t mod_sub_neon(uint32x4_t a, uint32x4_t b, uint32x4_t mod_vec) {
    uint32x4_t diff = vsubq_u32(a, b);
    // 检查是否发生下溢（a < b）
    uint32x4_t mask = vcltq_u32(a, b);
    return vbslq_u32(mask, vaddq_u32(diff, mod_vec), diff);
}
#endif

/* ============================== SIMD优化的NTT实现 ============================== */

/**
 * @brief SIMD优化的并行NTT实现
 * @param a 输入/输出向量
 * @param inverse 是否为逆变换
 * @param br Barrett对象
 * @param ctx MPI上下文
 * @param g 原根（通常为3）
 */
void ntt_simd_parallel(vector<u32>& a, bool inverse, const Barrett& br,
                      const MPIContext& ctx, u32 g = 3) {
    int n = a.size();

    // 位反转（仅在rank 0执行，然后广播）
    if(ctx.rank == 0) {
        vector<int> rev;
        bit_reverse(rev, n);
        for(int i = 0; i < n; ++i) {
            if(i < rev[i]) swap(a[i], a[rev[i]]);
        }
    }
    MPI_Bcast(a.data(), n, MPI_UNSIGNED, 0, ctx.comm);

#ifdef __ARM_NEON
    uint32x4_t mod_vec = vdupq_n_u32(br.mod);
#endif

    // 分布式蝶形运算
    for(int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        u32 wn = mod_pow(g, (br.mod-1)/len, br.mod);
        if(inverse) wn = mod_pow(wn, br.mod-2, br.mod);

        // 计算本进程负责的块数和范围
        int total_blocks = n / len;
        int blocks_per_proc = total_blocks / ctx.size;
        int remainder_blocks = total_blocks % ctx.size;

        int my_blocks = blocks_per_proc + (ctx.rank < remainder_blocks ? 1 : 0);
        int start_block = ctx.rank * blocks_per_proc + min(ctx.rank, remainder_blocks);

        // 本地SIMD优化的蝶形运算
        for(int b = 0; b < my_blocks; ++b) {
            int block_start = (start_block + b) * len;
            u32 w = 1;
            int j = 0;

#ifdef __ARM_NEON
            // SIMD处理：每次处理4个蝶形运算
            for(; j + 3 < m; j += 4) {
                // 加载数据
                uint32x4_t u_vec = vld1q_u32(&a[block_start + j]);
                uint32x4_t v_input_vec = vld1q_u32(&a[block_start + j + m]);

                // 计算旋转因子w, w*wn, w*wn^2, w*wn^3
                u32 w_terms[4];
                w_terms[0] = w;
                w_terms[1] = br.mul(w_terms[0], wn);
                w_terms[2] = br.mul(w_terms[1], wn);
                w_terms[3] = br.mul(w_terms[2], wn);
                uint32x4_t w_vec = vld1q_u32(w_terms);

                // v = v_input * w (SIMD Barrett乘法)
                uint32x4_t v_vec = barrett_mul_neon(v_input_vec, w_vec, br);

                // 蝶形运算：u + v, u - v
                uint32x4_t u_plus_v = mod_add_neon(u_vec, v_vec, mod_vec);
                uint32x4_t u_minus_v = mod_sub_neon(u_vec, v_vec, mod_vec);

                // 存储结果
                vst1q_u32(&a[block_start + j], u_plus_v);
                vst1q_u32(&a[block_start + j + m], u_minus_v);

                // 更新w
                w = br.mul(w_terms[3], wn);
            }
#endif

            // 处理剩余的元素（标量）
            for(; j < m; ++j) {
                u32 u = a[block_start + j];
                u32 v = br.mul(a[block_start + j + m], w);
                a[block_start + j] = br.add(u, v);
                a[block_start + j + m] = br.sub(u, v);
                w = br.mul(w, wn);
            }
        }

        // 同步所有进程的计算结果
        vector<int> recvcounts(ctx.size), displs(ctx.size);
        for(int r = 0; r < ctx.size; ++r) {
            int r_blocks = blocks_per_proc + (r < remainder_blocks ? 1 : 0);
            recvcounts[r] = r_blocks * len;
            displs[r] = (r * blocks_per_proc + min(r, remainder_blocks)) * len;
        }

        vector<u32> temp_data(my_blocks * len);
        if(my_blocks > 0) {
            memcpy(temp_data.data(), a.data() + start_block * len,
                   my_blocks * len * sizeof(u32));
        }

        MPI_Allgatherv(temp_data.data(), my_blocks * len, MPI_UNSIGNED,
                       a.data(), recvcounts.data(), displs.data(), MPI_UNSIGNED,
                       ctx.comm);
    }

    // 逆变换的最后一步：除以n
    if(inverse) {
        if(ctx.rank == 0) {
            u32 inv_n = mod_pow(n, br.mod-2, br.mod);
#ifdef __ARM_NEON
            // SIMD优化的标量乘法
            uint32x4_t inv_n_vec = vdupq_n_u32(inv_n);
            int i = 0;
            for(; i + 3 < n; i += 4) {
                uint32x4_t x_vec = vld1q_u32(&a[i]);
                uint32x4_t result = barrett_mul_neon(x_vec, inv_n_vec, br);
                vst1q_u32(&a[i], result);
            }
            for(; i < n; ++i) {
                a[i] = br.mul(a[i], inv_n);
            }
#else
            for(u32 &x : a) x = br.mul(x, inv_n);
#endif
        }
        MPI_Bcast(a.data(), n, MPI_UNSIGNED, 0, ctx.comm);
    }
}

/* ============================== 标量版本NTT（用于性能对比） ============================== */
void ntt_scalar_parallel(vector<u32>& a, bool inverse, const Barrett& br,
                         const MPIContext& ctx, u32 g = 3) {
    int n = a.size();

    // 位反转（仅在rank 0执行，然后广播）
    if(ctx.rank == 0) {
        vector<int> rev;
        bit_reverse(rev, n);
        for(int i = 0; i < n; ++i) {
            if(i < rev[i]) swap(a[i], a[rev[i]]);
        }
    }
    MPI_Bcast(a.data(), n, MPI_UNSIGNED, 0, ctx.comm);

    // 分布式蝶形运算
    for(int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        u32 wn = mod_pow(g, (br.mod-1)/len, br.mod);
        if(inverse) wn = mod_pow(wn, br.mod-2, br.mod);

        // 计算本进程负责的块数和范围
        int total_blocks = n / len;
        int blocks_per_proc = total_blocks / ctx.size;
        int remainder_blocks = total_blocks % ctx.size;

        int my_blocks = blocks_per_proc + (ctx.rank < remainder_blocks ? 1 : 0);
        int start_block = ctx.rank * blocks_per_proc + min(ctx.rank, remainder_blocks);

        // 本地蝶形运算
        for(int b = 0; b < my_blocks; ++b) {
            int block_start = (start_block + b) * len;
            u32 w = 1;
            for(int j = 0; j < m; ++j) {
                u32 u = a[block_start + j];
                u32 v = br.mul(a[block_start + j + m], w);
                a[block_start + j] = br.add(u, v);
                a[block_start + j + m] = br.sub(u, v);
                w = br.mul(w, wn);
            }
        }

        // 同步所有进程的计算结果
        vector<int> recvcounts(ctx.size), displs(ctx.size);
        for(int r = 0; r < ctx.size; ++r) {
            int r_blocks = blocks_per_proc + (r < remainder_blocks ? 1 : 0);
            recvcounts[r] = r_blocks * len;
            displs[r] = (r * blocks_per_proc + min(r, remainder_blocks)) * len;
        }

        vector<u32> temp_data(my_blocks * len);
        if(my_blocks > 0) {
            memcpy(temp_data.data(), a.data() + start_block * len,
                   my_blocks * len * sizeof(u32));
        }

        MPI_Allgatherv(temp_data.data(), my_blocks * len, MPI_UNSIGNED,
                       a.data(), recvcounts.data(), displs.data(), MPI_UNSIGNED,
                       ctx.comm);
    }

    // 逆变换的最后一步：除以n
    if(inverse) {
        if(ctx.rank == 0) {
            u32 inv_n = mod_pow(n, br.mod-2, br.mod);
            for(u32 &x : a) x = br.mul(x, inv_n);
        }
        MPI_Bcast(a.data(), n, MPI_UNSIGNED, 0, ctx.comm);
    }
}

/* ============================== 并行多项式乘法 ============================== */
void poly_multiply_simd_parallel(const int* a, const int* b, int* ab, int n, int p,
                                 const MPIContext& ctx) {
    Barrett br(p);
    int lim = 1;
    while(lim < 2*n) lim <<= 1;

    vector<u32> A(lim, 0), B(lim, 0);

    // 只在rank 0进行数据初始化
    if(ctx.rank == 0) {
        for(int i = 0; i < n; ++i) {
            A[i] = ((a[i] % p) + p) % p;
            B[i] = ((b[i] % p) + p) % p;
        }
    }

    // 广播初始数据到所有进程
    MPI_Bcast(A.data(), lim, MPI_UNSIGNED, 0, ctx.comm);
    MPI_Bcast(B.data(), lim, MPI_UNSIGNED, 0, ctx.comm);

    // 并行SIMD NTT正变换
    ntt_simd_parallel(A, false, br, ctx);
    ntt_simd_parallel(B, false, br, ctx);

    // 点乘（每个进程都执行完整的点乘）
#ifdef __ARM_NEON
    // SIMD优化的点乘
    int i = 0;
    for(; i + 3 < lim; i += 4) {
        uint32x4_t a_vec = vld1q_u32(&A[i]);
        uint32x4_t b_vec = vld1q_u32(&B[i]);
        uint32x4_t result = barrett_mul_neon(a_vec, b_vec, br);
        vst1q_u32(&A[i], result);
    }
    for(; i < lim; ++i) {
        A[i] = br.mul(A[i], B[i]);
    }
#else
    for(int i = 0; i < lim; ++i) {
        A[i] = br.mul(A[i], B[i]);
    }
#endif

    // 并行SIMD NTT逆变换
    ntt_simd_parallel(A, true, br, ctx);

    // 只在rank 0复制结果
    if(ctx.rank == 0) {
        for(int i = 0; i < 2*n-1; ++i) {
            ab[i] = static_cast<int>(A[i]);
        }
    }
}

void poly_multiply_scalar_parallel(const int* a, const int* b, int* ab, int n, int p,
                                  const MPIContext& ctx) {
    Barrett br(p);
    int lim = 1;
    while(lim < 2*n) lim <<= 1;

    vector<u32> A(lim, 0), B(lim, 0);

    // 只在rank 0进行数据初始化
    if(ctx.rank == 0) {
        for(int i = 0; i < n; ++i) {
            A[i] = ((a[i] % p) + p) % p;
            B[i] = ((b[i] % p) + p) % p;
        }
    }

    // 广播初始数据到所有进程
    MPI_Bcast(A.data(), lim, MPI_UNSIGNED, 0, ctx.comm);
    MPI_Bcast(B.data(), lim, MPI_UNSIGNED, 0, ctx.comm);

    // 并行标量NTT正变换
    ntt_scalar_parallel(A, false, br, ctx);
    ntt_scalar_parallel(B, false, br, ctx);

    // 点乘
    for(int i = 0; i < lim; ++i) {
        A[i] = br.mul(A[i], B[i]);
    }

    // 并行标量NTT逆变换
    ntt_scalar_parallel(A, true, br, ctx);

    // 只在rank 0复制结果
    if(ctx.rank == 0) {
        for(int i = 0; i < 2*n-1; ++i) {
            ab[i] = static_cast<int>(A[i]);
        }
    }
}

/* ============================== 全局静态缓冲区 ============================== */
static int a[300000], b[300000], ab[600000];

/* ============================== 主函数 ============================== */
int main(int argc, char* argv[]) {
    MPI_Init(&argc, &argv);

    MPIContext ctx;
    MPI_Comm_rank(MPI_COMM_WORLD, &ctx.rank);
    MPI_Comm_size(MPI_COMM_WORLD, &ctx.size);
    ctx.comm = MPI_COMM_WORLD;

    const int first = 0;
    const int last  = 3;

    if(ctx.rank == 0) {
        cout << "MPI + SIMD 并行 Barrett Radix-2 NTT, ranks = " << ctx.size << '\n';
#ifdef __ARM_NEON
        cout << "SIMD优化: ARM NEON 已启用\n";
#else
        cout << "SIMD优化: 未启用（回退到标量实现）\n";
#endif
        cout << "对比标量和SIMD优化的NTT性能\n";
        cout << "=" << string(60, '=') << '\n';
        cout.flush();
    }

    /* 处理所有测试用例 */
    for(int id = first; id <= last; ++id) {
        int n, p;

        // 只有rank 0读取数据
        if(ctx.rank == 0) {
            fRead(a, b, &n, &p, id);
        }

        // 广播n和p到所有进程
        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p, 1, MPI_INT, 0, ctx.comm);

        // 广播输入数据到所有进程
        MPI_Bcast(a, n, MPI_INT, 0, ctx.comm);
        MPI_Bcast(b, n, MPI_INT, 0, ctx.comm);

        MPI_Barrier(ctx.comm);

        // SIMD优化的并行NTT计算
        auto t0_simd = chrono::high_resolution_clock::now();
        poly_multiply_simd_parallel(a, b, ab, n, p, ctx);
        MPI_Barrier(ctx.comm);
        auto t1_simd = chrono::high_resolution_clock::now();
        double us_simd = chrono::duration<double, std::micro>(t1_simd - t0_simd).count();

        // 只在rank 0进行验证
        if(ctx.rank == 0) {
            /* 验证SIMD结果 */
            fCheck(ab, n, id);
        }

        /* 标量NTT性能对比（所有进程参与） */
        MPI_Barrier(ctx.comm);
        auto t0_scalar = chrono::high_resolution_clock::now();
        poly_multiply_scalar_parallel(a, b, ab, n, p, ctx);
        MPI_Barrier(ctx.comm);
        auto t1_scalar = chrono::high_resolution_clock::now();
        double us_scalar = chrono::duration<double, std::micro>(t1_scalar - t0_scalar).count();

        // 只在rank 0输出结果
        if(ctx.rank == 0) {
            /* 输出性能对比结果 */
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p << "):\n";
            cout << "  标量NTT:   " << fixed << setprecision(2) << us_scalar << " us\n";
            cout << "  SIMD NTT:  " << fixed << setprecision(2) << us_simd << " us\n";
            cout << "  加速比:    " << fixed << setprecision(2) << us_scalar/us_simd << "x\n";
            cout << "  效率:      " << fixed << setprecision(2)
                 << (us_scalar/us_simd)/ctx.size*100 << "%\n";
            cout << string(60, '-') << '\n';
            cout.flush();

            /* 写入结果文件 */
            fWrite(ab, n, id);
        }
    }

    MPI_Barrier(ctx.comm);
    if(ctx.rank == 0) {
        cout << "全部用例处理完成。\n";
        cout << "并行化策略: MPI数据级并行 + SIMD向量化优化\n";
        cout << "优化技术: Barrett快速取模 + NEON向量化蝶形运算\n";
    }

    MPI_Finalize();
    return 0;
}
