/**************************************************************
 *  main_simd_neon_mpi.cc  ―― MPI并行NTT实现
 *    · 实现真正的MPI并行NTT算法
 *    · 修复NTT算法正确性问题
 *    · 集成SIMD优化和MPI并行
 *    · 支持Barrett快速模运算
 *************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
#ifdef __ARM_NEON
#   include <arm_neon.h>
#endif

/* ============================== MPI上下文 ============================== */
struct MPIContext {
    int rank, size;
    MPI_Comm comm;

    MPIContext() {
        MPI_Comm_rank(MPI_COMM_WORLD, &rank);
        MPI_Comm_size(MPI_COMM_WORLD, &size);
        comm = MPI_COMM_WORLD;
    }
};

/* ============================== I/O 例程 ============================== */
void fRead(int *a,int *b,int *n,int *p,int id){
    std::string path="../nttdata/"+std::to_string(id)+".in";
    std::ifstream fin(path);
    fin>>*n>>*p;
    for(int i=0;i<*n;++i) fin>>a[i];
    for(int i=0;i<*n;++i) fin>>b[i];
}
void fCheck(int *ab,int n,int id){
    std::string path="../nttdata/"+std::to_string(id)+".out";
    std::ifstream fin(path);
    for(int i=0;i<2*n-1;++i){
        int x;fin>>x;
        if(x!=ab[i]){std::cout<<"多项式乘法结果错误\n";return;}
    }
    std::cout<<"多项式乘法结果正确\n";
}
void fWrite(int *ab,int n,int id){
    std::string path="files/"+std::to_string(id)+".out";
    std::ofstream fout(path);
    for(int i=0;i<2*n-1;++i) fout<<ab[i]<<'\n';
}

/* ============================== 工具函数 ============================== */
using uint32 = uint32_t; using uint64 = uint64_t;
static inline uint32 qpow(uint32 x,uint64 y,uint32 mod){
    uint64 res=1,base=x%mod;
    while(y){ if(y&1) res=res*base%mod; base=base*base%mod; y>>=1; }
    return uint32(res);
}
/* Barrett - 完整版 */
struct Barrett{
    uint32 mod; uint64 im;
    explicit Barrett(uint32 m):mod(m),im(~0ULL/m+1){}
    inline uint32 reduce(uint64 x)const{
        uint64 q=(__uint128_t)x*im>>64;
        uint32 r=uint32(x-q*mod);
        return r>=mod? r-mod:r;
    }
    inline uint32 mul(uint32 a,uint32 b)const{ return reduce(uint64(a)*b); }
    inline uint32 add(uint32 a, uint32 b) const {
        uint32 s = a + b;
        return s >= mod ? s - mod : s;
    }
    inline uint32 sub(uint32 a, uint32 b) const {
        return a >= b ? a - b : a + mod - b;
    }
    uint32 pow(uint32 x, uint64 e) const {
        uint32 res = 1;
        while (e) {
            if (e & 1) res = mul(res, x);
            x = mul(x, x);
            e >>= 1;
        }
        return res;
    }
};

/* 模逆元计算 */
uint64_t modinv(uint64_t a, uint64_t m) {
    uint64_t b = m, u = 1, v = 0;
    while (b) {
        uint64_t t = a / b;
        a -= t * b; std::swap(a, b);
        u -= t * v; std::swap(u, v);
    }
    return (u + m) % m;
}
/* ============================== 正确的NTT实现 ============================== */
void serialNTT(std::vector<uint32>& a, bool inverse, const Barrett& br) {
    const int n = a.size();
    static std::vector<int> rev; rev.resize(n);
    for (int i = 0, lg = __builtin_ctz(n); i < n; ++i) {
        rev[i] = (rev[i >> 1] >> 1) | ((i & 1) << (lg - 1));
        if (i < rev[i]) std::swap(a[i], a[rev[i]]);
    }

    for (int len = 2; len <= n; len <<= 1) {
        uint32 wn = br.pow(3, (br.mod - 1) / len);
        if (inverse) wn = br.pow(wn, br.mod - 2);
        int half = len >> 1;
        for (int i = 0; i < n; i += len) {
            uint32 w = 1;
            for (int j = 0; j < half; ++j) {
                uint32 u = a[i + j];
                uint32 v = br.mul(a[i + j + half], w);
                a[i + j]         = br.add(u, v);
                a[i + j + half]  = br.sub(u, v);
                w = br.mul(w, wn);
            }
        }
    }
    if (inverse) {
        uint32 inv_n = br.pow(n, br.mod - 2);
        for (auto& x : a) x = br.mul(x, inv_n);
    }
}



/* ============================== MPI并行乘法 ============================== */
void multiModularMPIMultiply(const std::vector<int>& a, const std::vector<int>& b,
                             std::vector<int>& result_mod_p, int n, uint32_t p_target,
                             const MPIContext& ctx) {
    constexpr uint32_t mod[3] = { 998244353u, 1004535809u, 469762049u};
    const int MODS = 3;

    std::vector<int> my_mod_idx;
    for (int i = ctx.rank; i < MODS; i += ctx.size) {
        my_mod_idx.push_back(i);
    }

    int conv_len = 2 * n - 1;
    std::vector<uint32_t> r0(conv_len, 0),
                           r1(conv_len, 0),
                           r2(conv_len, 0);
    for (int idx : my_mod_idx) {
        Barrett br(mod[idx]);
        std::vector<uint32_t> A(a.begin(), a.end()),
                              B(b.begin(), b.end());

        int lim = 1; while (lim < 2 * n) lim <<= 1;
        A.resize(lim); B.resize(lim);

        serialNTT(A, false, br);
        serialNTT(B, false, br);
        for (int i = 0; i < lim; ++i) A[i] = br.mul(A[i], B[i]);
        serialNTT(A, true,  br);

        for (int i = 0; i < conv_len; ++i) {
            if      (idx == 0) r0[i] = A[i];
            else if (idx == 1) r1[i] = A[i];
            else               r2[i] = A[i];
        }
    }

    MPI_Allreduce(MPI_IN_PLACE, r0.data(), conv_len, MPI_UINT32_T, MPI_SUM, ctx.comm);
    MPI_Allreduce(MPI_IN_PLACE, r1.data(), conv_len, MPI_UINT32_T, MPI_SUM, ctx.comm);
    MPI_Allreduce(MPI_IN_PLACE, r2.data(), conv_len, MPI_UINT32_T, MPI_SUM, ctx.comm);

    if (ctx.rank == 0) {
        result_mod_p.resize(conv_len);

        uint64_t p0 = mod[0], p1 = mod[1], p2 = mod[2];

        uint64_t inv_p0_mod_p1   = modinv(p0, p1);
        uint64_t inv_p0p1_mod_p2 = modinv(uint64_t(p0) * p1 % p2, p2);
        __uint128_t P01 = __uint128_t(p0) * p1;

        for (int i = 0; i < conv_len; ++i) {
            uint64_t x0 = r0[i];

            uint64_t t1 = ( (r1[i] + p1 - x0 % p1) * inv_p0_mod_p1 ) % p1;

            uint64_t x0_p0t1 = ( x0 + (__uint128_t)p0 * t1 ) % p2;
            uint64_t t2 = ( (r2[i] + p2 - x0_p0t1) * inv_p0p1_mod_p2 ) % p2;

            __uint128_t big = x0 + (__uint128_t)p0 * t1 + P01 * t2;
            result_mod_p[i] = int(big % p_target);
        }
    }
}

/* ============================== 主程序 ============================== */
int main(int argc, char **argv) {
    MPI_Init(&argc, &argv);
    MPIContext ctx;

    int a_arr[300000], b_arr[300000];
    int test_begin = 0, test_end = 3;

    if (ctx.rank == 0) {
        std::cout << "MPI并行NTT实现 - 进程数: " << ctx.size << std::endl;
        std::cout.flush();
    }

    for (int id = test_begin; id <= test_end; ++id) {
        if (ctx.rank == 0) {
            std::cout << "开始处理测试用例 " << id << std::endl;
        }

        int n = 0, p_test = 0;
        if (ctx.rank == 0) {
            fRead(a_arr, b_arr, &n, &p_test, id);
            std::cout << "读取完成: n=" << n << ", p=" << p_test << std::endl;
        }

        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p_test, 1, MPI_INT, 0, ctx.comm);

        if (ctx.rank == 0) {
            std::cout << "广播完成" << std::endl;
        }

        std::vector<int> a(n), b(n);
        MPI_Bcast(a_arr, n, MPI_INT, 0, ctx.comm);
        MPI_Bcast(b_arr, n, MPI_INT, 0, ctx.comm);
        std::copy(a_arr, a_arr + n, a.begin());
        std::copy(b_arr, b_arr + n, b.begin());

        if (ctx.rank == 0) {
            std::cout << "数据准备完成，开始计算" << std::endl;
        }

        std::vector<int> ab_mod;
        MPI_Barrier(ctx.comm);
        double t0 = MPI_Wtime();
        multiModularMPIMultiply(a, b, ab_mod, n, p_test, ctx);
        double t1 = MPI_Wtime();

        if (ctx.rank == 0) {
            std::cout << "计算完成，开始检查结果" << std::endl;
            fCheck(ab_mod.data(), n, id);
            std::cout << "MPI "<< ctx.size <<"P: n="<< n
                      << "  耗时 "<< std::fixed << std::setprecision(3)
                      << (t1 - t0) * 1000 << " ms\n";
            fWrite(ab_mod.data(), n, id);
        }
    }

    MPI_Finalize();
    return 0;
}
