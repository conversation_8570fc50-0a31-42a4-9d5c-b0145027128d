/****************************************************************************************
 * main_barrett_radix2_ntt.cc   ——  真正并行的 Barrett NTT (Radix-2, 任意 2^k 点)
 *
 *  1) 实现数据级并行的NTT算法，而非仅仅case级并行
 *  2) 使用行划分策略：将NTT数据分布到不同进程进行并行计算
 *  3) 在蝶形运算的每个层级进行必要的进程间通信和数据重分布
 *  4) 保留Barrett快速取模优化，确保数值计算的高效性
 *  5) 编译：mpic++ -O3 -std=c++20 -march=native main_barrett_radix2_ntt.cc -o ntt_parallel
 *     运行：mpirun -np 4 ./ntt_parallel       # 进程数按需调整
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
#include <iomanip>
#include <chrono>
using namespace std;

using u32  = uint32_t;
using u64  = uint64_t;
#if defined(_MSC_VER) && !defined(__clang__)
using u128 = unsigned __int128;
#else
using u128 = __uint128_t;
#endif

/* ---------------- 固定 I/O ---------------- */
void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if(!fin) { cerr << "无法打开输入文件: " << path << '\n'; MPI_Abort(MPI_COMM_WORLD, 1); }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}
void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if(!fin) { cerr << "无法打开输出文件: " << path << '\n'; MPI_Abort(MPI_COMM_WORLD, 1); }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; fin >> x;
        if (x != ab[i]) { cout << "多项式乘法结果错误 (id="<<input_id<<")\n"; return; }
    }
    cout << "多项式乘法结果正确 (id="<<input_id<<")\n";
}
void fWrite(int *ab, int n, int input_id) {
    string path = "files/" + to_string(input_id) + ".out";
    ofstream fout(path);
    for (int i = 0; i < 2 * n - 1; ++i) fout << ab[i] << '\n';
}

/* -------------- Barrett 快速取模 -------------- */
class Barrett {
public:
    explicit Barrett(u32 m): mod(m) {
        inv = (static_cast<u128>(1) << 64) / m;
    }
    inline u32 reduce(u64 x) const {
        u64 q = (static_cast<u128>(x) * inv) >> 64;
        u64 r = x - q * mod;
        if (r >= mod) r -= mod;
        return static_cast<u32>(r);
    }
    inline u32 mul(u32 a,u32 b) const {
        return reduce(static_cast<u64>(a)*b);
    }
    const u32 mod;
private:
    u64 inv;
};

/* -------------- 工具函数 ---------------------- */
static u32 mod_pow(u32 a, u64 e, u32 mod) {
    u64 res = 1, base = a;
    while (e) {
        if (e & 1) res = res * base % mod;
        base = base * base % mod;
        e >>= 1;
    }
    return static_cast<u32>(res);
}
static void bit_reverse(vector<int>& rev,int n) {
    int lg = __builtin_ctz(n);
    rev.resize(n);
    for (int i=0;i<n;++i) rev[i]=(rev[i>>1]>>1)|((i&1)<<(lg-1));
}

/* -------------- MPI 并行辅助函数 -------------- */
// 获取MPI上下文信息
struct MPIContext {
    int rank, size;
    MPI_Comm comm;
};

// 数据分布：将全局数据分发到各进程的本地缓冲区
void distribute_data(const vector<u32>& global_data, vector<u32>& local_data,
                    int n, const MPIContext& ctx) {
    int local_size = n / ctx.size;
    int remainder = n % ctx.size;

    // 计算每个进程的数据量和起始位置
    vector<int> sendcounts(ctx.size), displs(ctx.size);
    for(int i = 0; i < ctx.size; ++i) {
        sendcounts[i] = local_size + (i < remainder ? 1 : 0);
        displs[i] = i * local_size + min(i, remainder);
    }

    int my_count = sendcounts[ctx.rank];
    local_data.resize(my_count);

    MPI_Scatterv(global_data.data(), sendcounts.data(), displs.data(), MPI_UINT32_T,
                 local_data.data(), my_count, MPI_UINT32_T, 0, ctx.comm);
}

// 数据收集：将各进程的本地数据收集到全局缓冲区
void gather_data(vector<u32>& global_data, const vector<u32>& local_data,
                int n, const MPIContext& ctx) {
    int local_size = n / ctx.size;
    int remainder = n % ctx.size;

    vector<int> recvcounts(ctx.size), displs(ctx.size);
    for(int i = 0; i < ctx.size; ++i) {
        recvcounts[i] = local_size + (i < remainder ? 1 : 0);
        displs[i] = i * local_size + min(i, remainder);
    }

    if(ctx.rank == 0) global_data.resize(n);

    MPI_Gatherv(local_data.data(), local_data.size(), MPI_UINT32_T,
                global_data.data(), recvcounts.data(), displs.data(), MPI_UINT32_T,
                0, ctx.comm);
}

/* -------------- Radix-2 并行 NTT -------------- */
void ntt_parallel(vector<u32>& a, bool inverse, const Barrett& br,
                 const MPIContext& ctx, u32 g=3) {
    int n = a.size();

    // 位反转（仅在rank 0执行，然后广播）
    if(ctx.rank == 0) {
        vector<int> rev;
        bit_reverse(rev, n);
        for(int i = 0; i < n; ++i) {
            if(i < rev[i]) swap(a[i], a[rev[i]]);
        }
    }
    MPI_Bcast(a.data(), n, MPI_UINT32_T, 0, ctx.comm);

    // 分布式蝶形运算
    for(int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        u32 wn = mod_pow(g, (br.mod-1)/len, br.mod);
        if(inverse) wn = mod_pow(wn, br.mod-2, br.mod);

        // 计算本进程负责的块数和范围
        int total_blocks = n / len;
        int blocks_per_proc = total_blocks / ctx.size;
        int remainder_blocks = total_blocks % ctx.size;

        int my_blocks = blocks_per_proc + (ctx.rank < remainder_blocks ? 1 : 0);
        int start_block = ctx.rank * blocks_per_proc + min(ctx.rank, remainder_blocks);

        // 本地蝶形运算
        for(int b = 0; b < my_blocks; ++b) {
            int block_start = (start_block + b) * len;
            u32 w = 1;
            for(int j = 0; j < m; ++j) {
                u32 u = a[block_start + j];
                u32 v = br.mul(a[block_start + j + m], w);
                a[block_start + j] = u + v >= br.mod ? u + v - br.mod : u + v;
                a[block_start + j + m] = u >= v ? u - v : u + br.mod - v;
                w = br.mul(w, wn);
            }
        }

        // 同步所有进程的计算结果
        vector<int> recvcounts(ctx.size), displs(ctx.size);
        for(int r = 0; r < ctx.size; ++r) {
            int r_blocks = blocks_per_proc + (r < remainder_blocks ? 1 : 0);
            recvcounts[r] = r_blocks * len;
            displs[r] = (r * blocks_per_proc + min(r, remainder_blocks)) * len;
        }

        vector<u32> temp_data(my_blocks * len);
        if(my_blocks > 0) {
            memcpy(temp_data.data(), a.data() + start_block * len,
                   my_blocks * len * sizeof(u32));
        }

        MPI_Allgatherv(temp_data.data(), my_blocks * len, MPI_UINT32_T,
                       a.data(), recvcounts.data(), displs.data(), MPI_UINT32_T,
                       ctx.comm);
    }

    // 逆变换的最后一步：除以n
    if(inverse) {
        if(ctx.rank == 0) {
            u32 inv_n = mod_pow(n, br.mod-2, br.mod);
            for(u32 &x : a) x = br.mul(x, inv_n);
        }
        MPI_Bcast(a.data(), n, MPI_UINT32_T, 0, ctx.comm);
    }
}

/* -------------- 串行 NTT (保留用于对比) -------------- */
void ntt_serial(vector<u32>& a,bool inverse,const Barrett& br,u32 g=3) {
    int n=a.size();
    vector<int> rev; bit_reverse(rev,n);
    for(int i=0;i<n;++i) if(i<rev[i]) swap(a[i],a[rev[i]]);

    for(int len=2;len<=n;len<<=1){
        int m=len>>1;
        u32 wn=mod_pow(g,(br.mod-1)/len,br.mod);
        if(inverse) wn=mod_pow(wn,br.mod-2,br.mod);
        for(int i=0;i<n;i+=len){
            u32 w=1;
            for(int j=0;j<m;++j){
                u32 u=a[i+j];
                u32 v=br.mul(a[i+j+m],w);
                a[i+j]       = u+v>=br.mod?u+v-br.mod:u+v;
                a[i+j+m]     = u>=v?u-v:u+br.mod-v;
                w=br.mul(w,wn);
            }
        }
    }
    if(inverse){
        u32 inv_n=mod_pow(n,br.mod-2,br.mod);
        for(u32 &x:a) x=br.mul(x,inv_n);
    }
}

/* ------------ 并行多项式乘法 (Barrett NTT) -------- */
void poly_multiply_parallel(const int* a, const int* b, int* ab, int n, int p,
                           const MPIContext& ctx) {
    Barrett br(p);
    int lim = 1;
    while(lim < 2*n) lim <<= 1;

    vector<u32> A(lim, 0), B(lim, 0);

    // 只在rank 0进行数据初始化
    if(ctx.rank == 0) {
        for(int i = 0; i < n; ++i) {
            A[i] = ((a[i] % p) + p) % p;
            B[i] = ((b[i] % p) + p) % p;
        }
    }

    // 广播初始数据到所有进程
    MPI_Bcast(A.data(), lim, MPI_UINT32_T, 0, ctx.comm);
    MPI_Bcast(B.data(), lim, MPI_UINT32_T, 0, ctx.comm);

    // 并行NTT正变换
    ntt_parallel(A, false, br, ctx);
    ntt_parallel(B, false, br, ctx);

    // 点乘（每个进程都执行完整的点乘）
    for(int i = 0; i < lim; ++i) {
        A[i] = br.mul(A[i], B[i]);
    }

    // 并行NTT逆变换
    ntt_parallel(A, true, br, ctx);

    // 只在rank 0复制结果
    if(ctx.rank == 0) {
        for(int i = 0; i < 2*n-1; ++i) {
            ab[i] = static_cast<int>(A[i]);
        }
    }
}

/* ------------ 串行多项式乘法 (保留用于对比) -------- */
void poly_multiply_serial(const int* a,const int* b,int* ab,int n,int p){
    Barrett br(p);
    int lim=1; while(lim<2*n) lim<<=1;
    vector<u32> A(lim,0),B(lim,0);
    for(int i=0;i<n;++i){ A[i]=((a[i]%p)+p)%p; B[i]=((b[i]%p)+p)%p; }

    ntt_serial(A,false,br);
    ntt_serial(B,false,br);
    for(int i=0;i<lim;++i) A[i]=br.mul(A[i],B[i]);
    ntt_serial(A,true,br);

    for(int i=0;i<2*n-1;++i) ab[i]=static_cast<int>(A[i]);
}

/* ------------ 全局静态缓冲区 ------------------ */
static int a[300000],b[300000],ab[600000];

/* -------------------- main -------------------- */
int main(int argc,char* argv[]){
    MPI_Init(&argc,&argv);

    MPIContext ctx;
    MPI_Comm_rank(MPI_COMM_WORLD, &ctx.rank);
    MPI_Comm_size(MPI_COMM_WORLD, &ctx.size);
    ctx.comm = MPI_COMM_WORLD;

    const int first = 0;   /* 根据需要调整 */
    const int last  = 3;   /* ↑↑↑↑↑↑↑↑↑↑↑ */

    if(ctx.rank == 0){
        cout << "MPI 真正并行 Barrett NTT, ranks = " << ctx.size << '\n';
        cout << "对比串行和并行NTT性能\n";
        cout << "=" << string(50, '=') << '\n';
        cout.flush();
    }

    /* 处理所有测试用例，使用真正的并行NTT ------------------------------------- */
    for(int id = first; id <= last; ++id){
        int n, p;

        // 只有rank 0读取数据
        if(ctx.rank == 0) {
            fRead(a, b, &n, &p, id);
        }

        // 广播n和p到所有进程
        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p, 1, MPI_INT, 0, ctx.comm);

        // 广播输入数据到所有进程
        MPI_Bcast(a, n, MPI_INT, 0, ctx.comm);
        MPI_Bcast(b, n, MPI_INT, 0, ctx.comm);

        MPI_Barrier(ctx.comm);

        // 并行NTT计算
        auto t0_parallel = chrono::high_resolution_clock::now();
        poly_multiply_parallel(a, b, ab, n, p, ctx);
        MPI_Barrier(ctx.comm);
        auto t1_parallel = chrono::high_resolution_clock::now();
        double us_parallel = chrono::duration<double,std::micro>(t1_parallel-t0_parallel).count();

        // 只在rank 0进行验证和性能对比
        if(ctx.rank == 0) {
            /* 验证并行结果 */
            fCheck(ab, n, id);

            /* 串行NTT性能对比 */
            auto t0_serial = chrono::high_resolution_clock::now();
            poly_multiply_serial(a, b, ab, n, p);
            auto t1_serial = chrono::high_resolution_clock::now();
            double us_serial = chrono::duration<double,std::micro>(t1_serial-t0_serial).count();

            /* 输出性能对比结果 */
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p << "):\n";
            cout << "  串行NTT:   " << fixed << setprecision(2) << us_serial << " us\n";
            cout << "  并行NTT:   " << fixed << setprecision(2) << us_parallel << " us\n";
            cout << "  加速比:    " << fixed << setprecision(2) << us_serial/us_parallel << "x\n";
            cout << "  效率:      " << fixed << setprecision(2)
                 << (us_serial/us_parallel)/ctx.size*100 << "%\n";
            cout << string(50, '-') << '\n';
            cout.flush();

            /* 写入结果文件 */
            fWrite(ab, n, id);
        }
    }

    MPI_Barrier(ctx.comm);
    if(ctx.rank == 0) {
        cout << "全部用例处理完成。\n";
        cout << "并行化策略: 数据级并行，行划分 + 蝶形运算分布式计算\n";
    }

    MPI_Finalize();
    return 0;
}
