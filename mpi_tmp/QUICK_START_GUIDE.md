# SIMD优化MPI并行NTT快速开始指南

## 快速编译和运行

### 1. 编译所有SIMD版本
```bash
cd mpi_tmp
make simd
```

### 2. 运行测试
```bash
# 测试SIMD Radix-2版本（推荐）
make test-simd-radix2-4

# 测试SIMD Radix-4版本
make test-simd-radix4-4

# 运行完整性能对比
make test-compare
```

### 3. 查看帮助
```bash
make help
```

## 文件说明

### 核心实现文件
- `main_barrett_radix2_simd_mpi.cc` - SIMD优化的Radix-2 NTT实现
- `main_barrett_radix4_simd_mpi.cc` - SIMD优化的Radix-4 NTT实现

### 可执行文件
- `ntt_radix2_simd_mpi` - SIMD Radix-2版本
- `ntt_radix4_simd_mpi` - SIMD Radix-4版本
- `ntt_radix2_mpi` - 原始Radix-2版本（对比用）
- `ntt_radix4_mpi` - 原始Radix-4版本（对比用）

### 文档文件
- `README_SIMD_OPTIMIZATION.md` - 详细技术文档
- `SIMD_PERFORMANCE_REPORT.md` - 性能测试报告
- `QUICK_START_GUIDE.md` - 本快速指南

## 性能结果摘要

### SIMD Radix-2（推荐使用）
- **大规模数据加速比**: 1.45-1.55倍
- **并行效率**: 36-39%
- **适用场景**: 通用NTT计算

### SIMD Radix-4
- **大规模数据加速比**: 1.14-1.19倍
- **并行效率**: 28-30%
- **适用场景**: 特定优化需求

## 使用建议

### 1. 选择算法
- **推荐**: 使用SIMD Radix-2版本，性能最佳
- **特殊需求**: 如需要Radix-4特性，使用SIMD Radix-4版本

### 2. 进程数配置
- **推荐**: 4-8个MPI进程
- **注意**: 进程数过多可能导致通信开销增加

### 3. 数据规模
- **最佳**: n ≥ 65536的大规模数据
- **注意**: 小数据（n < 1024）SIMD优化效果有限

### 4. 硬件要求
- **必需**: 支持ARM NEON的处理器
- **推荐**: 多核ARM处理器，充足内存

## 故障排除

### 编译问题
```bash
# 检查MPI环境
which mpirun
which mpicxx

# 检查编译器支持
mpicxx --version
```

### 运行问题
```bash
# 检查测试数据
ls ../nttdata/

# 单进程测试
mpirun -np 1 ./ntt_radix2_simd_mpi

# 调试模式
mpirun -np 4 --debug ./ntt_radix2_simd_mpi
```

### 性能问题
- **检查CPU亲和性**: 确保进程绑定到不同核心
- **监控内存使用**: 避免内存不足导致的性能下降
- **调整进程数**: 根据硬件配置优化进程数量

## 扩展开发

### 添加新的SIMD优化
1. 在`barrett_mul_neon`函数中添加新的向量化操作
2. 在蝶形运算中集成新的SIMD指令
3. 测试和验证正确性

### 支持新的硬件平台
1. 添加对应的SIMD指令集支持（如AVX、SSE）
2. 修改编译条件和类型定义
3. 适配不同的向量宽度

### 算法改进
1. 实现Split-Radix算法的SIMD版本
2. 探索六步法NTT的向量化
3. 优化内存访问模式

## 联系和支持

如有问题或建议，请参考：
- 详细技术文档：`README_SIMD_OPTIMIZATION.md`
- 性能分析报告：`SIMD_PERFORMANCE_REPORT.md`
- 源代码注释：查看具体实现文件

---

**注意**: 本实现专为ARM NEON优化，在其他架构上可能需要相应调整。
