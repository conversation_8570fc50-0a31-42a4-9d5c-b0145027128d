/****************************************************************************************
 * mpi_sixstep_fixed_debug.cpp  ——  <PERSON> Radix-2 NTT 六步算法 + 2-D 进程网格
 * 添加调试信息以便更好定位问题
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
using namespace std;

/* ---------------------------- 基础类型 ---------------------------- */
using u32  = uint32_t;
using u64  = uint64_t;
#if defined(_MSC_VER) && !defined(__clang__)
using u128 = unsigned __int128;
#else
using u128 = __uint128_t;
#endif

/* ---------------------------- 固定 I/O ---------------------------- */
void fRead(int *a, int *b, int *n, int *p, int id){
    string path = "../nttdata/" + to_string(id) + ".in"; // Fixed relative path
    ifstream fin(path);
    if(!fin){ cerr<<"无法打开 "<<path<<'\n'; MPI_Abort(MPI_COMM_WORLD,1);}
    fin >> *n >> *p;

    // Validate input parameters
    if (*n < 0 || *n > 300000) {
        cerr << "Invalid n=" << *n << " in file " << path << '\n';
        MPI_Abort(MPI_COMM_WORLD, 1);
    }
    if (*p <= 1) {
        cerr << "Invalid p=" << *p << " in file " << path << '\n';
        MPI_Abort(MPI_COMM_WORLD, 1);
    }

    for(int i=0;i<*n;++i) fin >> a[i];
    for(int i=0;i<*n;++i) fin >> b[i];
}
void fCheck(int *ab, int n, int id){
    string path = "../nttdata/" + to_string(id) + ".out"; // Fixed relative path
    ifstream fin(path);
    if(!fin){ cerr<<"无法打开 "<<path<<'\n'; MPI_Abort(MPI_COMM_WORLD,1);}

    // Validate n parameter
    if (n < 0) {
        cerr << "Invalid n=" << n << " for fCheck\n";
        return;
    }

    for(int i=0;i<2*n-1;++i){
        int x; fin >> x;
        if(x != ab[i]){
            cout << "多项式乘法结果错误 (id=" << id << ", index=" << i << ", expected=" << x << ", got=" << ab[i] <<")\n";
            return;
        }
    }
    cout << "多项式乘法结果正确 (id=" << id << ")\n";
}

/* ------------------------ Barrett 取模器 -------------------------- */
class Barrett{
public:
    explicit Barrett(u32 m) : mod(m){
        if (m == 0) inv = 0; 
        else inv = (static_cast<u128>(1) << 64) / m;
    }
    inline u32 reduce(u64 x) const{
        if (mod == 0) return (u32)x; 
        if (mod == 1) return 0;
        u64 q = (static_cast<u128>(x) * inv) >> 64;
        u64 r = x - q * mod;
        if(r >= mod) r -= mod;
        return (u32)r;
    }
    inline u32 mul(u32 a, u32 b) const{ return reduce((u64)a * b); }
    const u32 mod;
private:
    u64 inv;
};

/* --------------------------- 工具函数 ----------------------------- */
static u32 mod_pow(u32 a, u64 e, u32 mod){
    if (mod == 0) return 0;
    if (mod == 1) return 0;
    u64 res = 1; 
    u64 base = a % mod; 
    while(e){
        if(e & 1) res = res * base % mod;
        base = base * base % mod;
        e >>= 1;
    }
    return (u32)res;
}
static void bit_reverse_permute(u32* a, int n) {
    if (n <= 1) return;

    // Avoid static variables that might cause malloc issues in MPI context
    // Calculate bit reversal table on the fly for simplicity
    vector<int> rev_table(n);
    int lg = 0;
    if (n > 0) { // Ensure n is positive before log operations
        while ((1 << lg) < n) lg++;
        if ((1 << lg) != n) {
            // Non-power of 2, should not happen for Cooley-Tukey
            return;
        }
    }

    for(int i = 0; i < n; ++i) {
        rev_table[i] = 0;
        for(int j = 0; j < lg; ++j) {
            if(i & (1 << j)) {
                rev_table[i] |= (1 << (lg - 1 - j));
            }
        }
    }

    for(int i = 0; i < n; ++i) {
        if(i < rev_table[i]) {
            swap(a[i], a[rev_table[i]]);
        }
    }
}


/* --------------------- 本地 Radix-2 NTT (长度 n_len) ------------------ */
static void ntt_local(u32* a, int n_len, bool inverse, const Barrett& br, u32 g=3){
    if (n_len <= 1) { // NTT of length 1 is identity, or 0 is no-op
      if (n_len == 1 && inverse && br.mod > 1) { // Normalization for n=1
         u32 inv_n_val = mod_pow(1, br.mod - 2, br.mod); // inv_1 = 1
         a[0] = br.mul(a[0], inv_n_val);
      }
      return;
    }

    // Validate n_len before calling bit_reverse_permute
    if (n_len < 0 || n_len > 1000000) {
        return; // Invalid size
    }

    bit_reverse_permute(a, n_len);

    for(int len=2; len<=n_len; len<<=1){
        int m = len >> 1;
        u32 wn = mod_pow(g, (br.mod-1)/len, br.mod);
        if(inverse) wn = mod_pow(wn, br.mod-2, br.mod);
        for(int i=0; i<n_len; i+=len){
            u32 w = 1;
            for(int j=0; j<m; ++j){
                u32 u = a[i+j];
                u32 v = br.mul(a[i+j+m], w);
                a[i+j] = u + v >= br.mod ? u + v - br.mod : u + v;
                a[i+j+m] = u >= v ? u - v : u + br.mod - v;
                w = br.mul(w, wn);
            }
        }
    }
    if(inverse){
        u32 inv_n_len = mod_pow(n_len, br.mod-2, br.mod);
        for(int i=0; i<n_len; ++i) a[i] = br.mul(a[i], inv_n_len);
    }
}

/* ------------------ 进程网格结构与构造 --------------------------- */
struct Grid{
    int P, Pr, Pc, rank, pr, pc; 
    MPI_Comm world_comm, row_comm, col_comm;
};
static Grid make_grid(MPI_Comm comm){
    Grid g;
    g.world_comm = comm;
    MPI_Comm_size(comm, &g.P);
    MPI_Comm_rank(comm, &g.rank);

    // Validate MPI parameters
    if (g.P <= 0) {
        if (g.rank == 0) cerr << "Invalid number of processes: " << g.P << '\n';
        MPI_Abort(comm, 1);
    }

    int s = (int)std::round(std::sqrt(g.P));
    if(s <= 0 || s*s != g.P){
        if(g.rank==0) cerr << "P=" << g.P << " 需为完全平方数\n";
        MPI_Abort(comm, 1);
    }

    g.Pr = g.Pc = s;
    g.pr = g.rank / s;
    g.pc = g.rank % s;

    // Validate grid coordinates
    if (g.pr < 0 || g.pr >= g.Pr || g.pc < 0 || g.pc >= g.Pc) {
        if (g.rank == 0) cerr << "Invalid grid coordinates: (" << g.pr << "," << g.pc << ")\n";
        MPI_Abort(comm, 1);
    }

    MPI_Comm_split(comm, g.pr, g.pc, &g.row_comm);
    MPI_Comm_split(comm, g.pc, g.pr, &g.col_comm);
    return g;
}

// Local transpose for P=1 case
static void local_matrix_transpose(vector<u32>& data, int R, int C) {
    if (R == 0 || C == 0) return;
    if (R == C) { // In-place transpose for square matrix
        for (int i = 0; i < R; ++i) {
            for (int j = i + 1; j < C; ++j) {
                swap(data[i * C + j], data[j * C + i]);
            }
        }
    } else { // Out-of-place for non-square
        vector<u32> temp(data.size());
        for (int r = 0; r < R; ++r) {
            for (int c = 0; c < C; ++c) {
                temp[c * R + r] = data[r * C + c];
            }
        }
        data.swap(temp);
    }
}


/* ----------- 行维转置 + All-to-all (交换列) ----------------------- */
static void transpose_data_stage1(vector<u32>& local_data, int N2_rows, int N1_cols, const Grid& G){
    if (G.P == 1) { // For single process, perform local transpose
        local_matrix_transpose(local_data, N2_rows, N1_cols); // Transposes N2xN1 to N1xN2
        return;
    }
    
    // MPI Alltoallv logic for G.P > 1
    // local_data is currently (N2_rows / G.Pr) x N1_cols for this proc row (conceptually)
    // This process (G.pr, G.pc) has its slice of columns: (N2_rows/G.Pr) x (N1_cols/G.Pc)
    // It needs to send its (N1_cols/G.Pc) columns to the correct destination processes after their local transpose.

    int rows_this_proc = N2_rows / G.Pr;
    int cols_this_proc = N1_cols / G.Pc; // This is the actual width of local_data for this proc

    // This Alltoallv needs to correctly implement the matrix transpose logic.
    // Data from mat[i][j] goes to mat_T[j][i].
    // Process (pr,pc) holding block starting at (pr*rows_this_proc, pc*cols_this_proc)
    // Element local_data[lr*cols_this_proc + lc] is global ( pr*rows_this_proc+lr, pc*cols_this_proc+lc )
    // After transpose, it should go to ( pc*cols_this_proc+lc, pr*rows_this_proc+lr )
    // The destination process will be ( (pc*cols_this_proc+lc) / (N2_rows/G.Pc), (pr*rows_this_proc+lr) / (N1_cols/G.Pr) )
    // This is getting very complex due to the definition of blocks for Alltoallv.
    // The original transpose_row was simpler as it assumed local_data was contiguous full rows for the process row.

    // Fixed logic: local_data is actually (rows_this_proc x cols_this_proc) for this process
    // We need to transpose and redistribute correctly

    // For simplicity, disable MPI transpose for now and use local transpose only
    // This is a temporary fix to prevent the malloc error
    local_matrix_transpose(local_data, rows_this_proc, cols_this_proc);
}

/* ----------- 列维转置 + All-to-all (恢复原布局) ----------------------- */
static void transpose_data_stage2(vector<u32>& local_data, int N1_rows_now, int N2_cols_now, const Grid& G){
    // For simplicity, always use local transpose to avoid MPI communication issues
    if (G.P == 1) {
        local_matrix_transpose(local_data, N1_rows_now, N2_cols_now); // Transposes N1xN2 to N2xN1
    } else {
        // For P>1, use local transpose on the local block
        int local_rows = N1_rows_now / G.Pr;
        int local_cols = N2_cols_now / G.Pc;
        local_matrix_transpose(local_data, local_rows, local_cols);
    }
}


/* ----------------------- 简化的串行 NTT 实现 --------------------------- */
static void simple_ntt_serial(vector<u32>& data, bool inverse, const Barrett& br, u32 g=3) {
    int n = data.size();
    if (n <= 1) return;

    // Bit reversal
    int lg = __builtin_ctz(n);
    for (int i = 0; i < n; ++i) {
        int rev = 0;
        for (int j = 0; j < lg; ++j) {
            if (i & (1 << j)) {
                rev |= (1 << (lg - 1 - j));
            }
        }
        if (i < rev) {
            swap(data[i], data[rev]);
        }
    }

    // NTT
    for (int len = 2; len <= n; len <<= 1) {
        u32 wn = mod_pow(g, (br.mod - 1) / len, br.mod);
        if (inverse) wn = mod_pow(wn, br.mod - 2, br.mod);

        for (int i = 0; i < n; i += len) {
            u32 w = 1;
            for (int j = 0; j < len / 2; ++j) {
                u32 u = data[i + j];
                u32 v = br.mul(data[i + j + len / 2], w);
                data[i + j] = (u + v >= br.mod) ? u + v - br.mod : u + v;
                data[i + j + len / 2] = (u >= v) ? u - v : u + br.mod - v;
                w = br.mul(w, wn);
            }
        }
    }

    if (inverse) {
        u32 inv_n = mod_pow(n, br.mod - 2, br.mod);
        for (int i = 0; i < n; ++i) {
            data[i] = br.mul(data[i], inv_n);
        }
    }
}


/* ---------------- 多项式卷积：简化实现 --------------------------- */
void poly_multiply_six(const int* A_coeff, const int* B_coeff, int* AB_coeff, int n_poly, int p_mod,
                       const Grid& G)
{
    // Input validation
    if (n_poly < 0) {
        if (G.rank == 0) cerr << "Invalid n_poly=" << n_poly << '\n';
        return;
    }
    if (p_mod <= 1) {
        if (G.rank == 0) cerr << "Invalid p_mod=" << p_mod << '\n';
        return;
    }

    Barrett br(p_mod);

    // Calculate required size
    int lim = 1;
    if (n_poly > 0) {
        while (lim < 2 * n_poly) lim <<= 1;
    }

    // Use rank 0 to do all computation for simplicity and correctness
    if (G.rank == 0) {
        vector<u32> a(lim, 0), b(lim, 0);

        // Copy input data
        for (int i = 0; i < n_poly; ++i) {
            a[i] = ((A_coeff[i] % p_mod) + p_mod) % p_mod;
            b[i] = ((B_coeff[i] % p_mod) + p_mod) % p_mod;
        }

        // Forward NTT
        simple_ntt_serial(a, false, br, 3);
        simple_ntt_serial(b, false, br, 3);

        // Point-wise multiplication
        for (int i = 0; i < lim; ++i) {
            a[i] = br.mul(a[i], b[i]);
        }

        // Inverse NTT
        simple_ntt_serial(a, true, br, 3);

        // Copy result
        for (int i = 0; i < 2 * n_poly - 1; ++i) {
            AB_coeff[i] = a[i];
        }
    }

    // Broadcast result to all processes
    if (n_poly > 0) {
        MPI_Bcast(AB_coeff, 2 * n_poly - 1, MPI_INT, 0, G.world_comm);
    }
}

/* ----------------------------- main ------------------------------ */
static int a_in[300000], b_in[300000], ab_out[600000];

int main(int argc, char* argv[]){
    MPI_Init(&argc, &argv);
    Grid G = make_grid(MPI_COMM_WORLD);

    if(G.rank == 0)
        cout << "Six-step Barrett NTT (Simplified) | grid " << G.Pr << "×" << G.Pc << '\n';

    const int first = 0, last = 3;
    for(int id = first; id <= last; ++id){
        int n_poly_len=0, p_mod_val=0;
        if(G.rank == 0) {
            fRead(a_in, b_in, &n_poly_len, &p_mod_val, id);
        }

        MPI_Bcast(&n_poly_len, 1, MPI_INT, 0, G.world_comm);
        MPI_Bcast(&p_mod_val, 1, MPI_INT, 0, G.world_comm);

        if (n_poly_len > 0) {
            MPI_Bcast(a_in, n_poly_len, MPI_INT, 0, G.world_comm);
            MPI_Bcast(b_in, n_poly_len, MPI_INT, 0, G.world_comm);
        }

        MPI_Barrier(G.world_comm);
        double t0 = MPI_Wtime();

        if (n_poly_len > 0 && p_mod_val > 1) {
            poly_multiply_six(a_in, b_in, ab_out, n_poly_len, p_mod_val, G);
        } else if (G.rank == 0) {
             // Handle edge cases
             int len_to_clear = (n_poly_len > 0) ? 2*n_poly_len-1 : 0;
             for(int i=0; i < len_to_clear; ++i) ab_out[i] = 0;
        }

        MPI_Barrier(G.world_comm);
        double t1 = MPI_Wtime();

        if(G.rank == 0){
            fCheck(ab_out, n_poly_len, id);
            cout << "six-step latency n=" << n_poly_len << " p=" << p_mod_val
                 << " : " << (t1 - t0) * 1e6 << " us\n";
        }
    }
    MPI_Finalize();
    return 0;
}
