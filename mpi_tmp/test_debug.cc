#include <bits/stdc++.h>
#include <mpi.h>

struct MPIContext {
    int rank, size;
    MPI_Comm comm;
    
    MPIContext() {
        MPI_Comm_rank(MPI_COMM_WORLD, &rank);
        MPI_Comm_size(MPI_COMM_WORLD, &size);
        comm = MPI_COMM_WORLD;
    }
};

void fRead(int *a,int *b,int *n,int *p,int id){
    std::string path="../nttdata/"+std::to_string(id)+".in";
    std::ifstream fin(path);
    fin>>*n>>*p;
    for(int i=0;i<*n;++i) fin>>a[i];
    for(int i=0;i<*n;++i) fin>>b[i];
}

int main(int argc, char **argv) {
    std::cout << "程序开始" << std::endl;
    
    MPI_Init(&argc, &argv);
    std::cout << "MPI初始化完成" << std::endl;
    
    MPIContext ctx;
    std::cout << "MPIContext创建完成" << std::endl;

    int a_arr[300000], b_arr[300000];
    int test_begin = 0, test_end = 0; // 只测试第一个

    if (ctx.rank == 0) {
        std::cout << "MPI并行NTT实现 - 进程数: " << ctx.size << std::endl;
        std::cout.flush();
    }

    for (int id = test_begin; id <= test_end; ++id) {
        std::cout << "开始处理测试用例 " << id << std::endl;
        
        int n = 0, p_test = 0;
        if (ctx.rank == 0) {
            std::cout << "开始读取文件" << std::endl;
            fRead(a_arr, b_arr, &n, &p_test, id);
            std::cout << "文件读取完成: n=" << n << ", p=" << p_test << std::endl;
        }
        
        std::cout << "开始广播" << std::endl;
        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p_test, 1, MPI_INT, 0, ctx.comm);
        std::cout << "广播完成" << std::endl;
        
        if (ctx.rank == 0) {
            std::cout << "测试用例 " << id << " 处理完成" << std::endl;
        }
    }

    std::cout << "开始清理MPI" << std::endl;
    MPI_Finalize();
    std::cout << "程序结束" << std::endl;
    return 0;
}
