#include <iostream>
#include <fstream>
#include <mpi.h>

int main(int argc, char** argv) {
    std::cout << "程序开始" << std::endl;
    
    MPI_Init(&argc, &argv);
    
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);
    
    if (rank == 0) {
        std::cout << "MPI初始化成功，进程数: " << size << std::endl;
        
        // 测试文件读取
        std::ifstream fin("../nttdata/0.in");
        if (fin.is_open()) {
            int n, p;
            fin >> n >> p;
            std::cout << "成功读取文件: n=" << n << ", p=" << p << std::endl;
            fin.close();
        } else {
            std::cout << "无法打开文件" << std::endl;
        }
    }
    
    MPI_Finalize();
    
    if (rank == 0) {
        std::cout << "程序结束" << std::endl;
    }
    
    return 0;
}
