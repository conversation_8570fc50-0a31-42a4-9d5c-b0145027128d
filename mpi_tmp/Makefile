CXX = mpicxx
CXXFLAGS = -O3 -std=c++17 -Wall -Wextra -march=native
TARGET = main_DIF_DIT_mpi
SOURCE = main_DIF_DIT_mpi.cc

# SIMD优化版本的目标
SIMD_RADIX2_TARGET = ntt_radix2_simd_mpi
SIMD_RADIX2_SOURCE = main_barrett_radix2_simd_mpi.cc
SIMD_RADIX4_TARGET = ntt_radix4_simd_mpi
SIMD_RADIX4_SOURCE = main_barrett_radix4_simd_mpi.cc

# 原始版本的目标
RADIX2_TARGET = ntt_radix2_mpi
RADIX2_SOURCE = main_barrett_radix2_ntt.cc
RADIX4_TARGET = ntt_radix4_mpi
RADIX4_SOURCE = main_barrett_radix4_ntt.cc

.PHONY: all clean test test1 test2 test4 test8 simd radix2 radix4 simd-radix2 simd-radix4 test-simd

all: $(TARGET)

# 原始目标
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE)

# SIMD优化版本
simd: simd-radix2 simd-radix4

simd-radix2: $(SIMD_RADIX2_TARGET)
$(SIMD_RADIX2_TARGET): $(SIMD_RADIX2_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(SIMD_RADIX2_TARGET) $(SIMD_RADIX2_SOURCE)

simd-radix4: $(SIMD_RADIX4_TARGET)
$(SIMD_RADIX4_TARGET): $(SIMD_RADIX4_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(SIMD_RADIX4_TARGET) $(SIMD_RADIX4_SOURCE)

# 原始版本（用于对比）
radix2: $(RADIX2_TARGET)
$(RADIX2_TARGET): $(RADIX2_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(RADIX2_TARGET) $(RADIX2_SOURCE)

radix4: $(RADIX4_TARGET)
$(RADIX4_TARGET): $(RADIX4_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(RADIX4_TARGET) $(RADIX4_SOURCE)

# SIMD测试目标
test-simd: test-simd-radix2 test-simd-radix4

test-simd-radix2: $(SIMD_RADIX2_TARGET)
	@echo "运行SIMD优化Radix-2 NTT性能测试..."
	@for np in 1 2 4 8; do \
		echo "==================="; \
		echo "SIMD Radix-2: 使用 $$np 个进程运行:"; \
		echo "==================="; \
		mpirun -np $$np ./$(SIMD_RADIX2_TARGET); \
		echo ""; \
	done

test-simd-radix4: $(SIMD_RADIX4_TARGET)
	@echo "运行SIMD优化Radix-4 NTT性能测试..."
	@for np in 1 2 4 8; do \
		echo "==================="; \
		echo "SIMD Radix-4: 使用 $$np 个进程运行:"; \
		echo "==================="; \
		mpirun -np $$np ./$(SIMD_RADIX4_TARGET); \
		echo ""; \
	done

# 性能对比测试
test-compare: simd radix2 radix4
	@echo "运行性能对比测试..."
	@echo "=================================="
	@echo "原始Radix-2 vs SIMD Radix-2"
	@echo "=================================="
	@echo "原始Radix-2 (4进程):"
	@mpirun -np 4 ./$(RADIX2_TARGET)
	@echo ""
	@echo "SIMD Radix-2 (4进程):"
	@mpirun -np 4 ./$(SIMD_RADIX2_TARGET)
	@echo ""
	@echo "=================================="
	@echo "原始Radix-4 vs SIMD Radix-4"
	@echo "=================================="
	@echo "原始Radix-4 (4进程):"
	@mpirun -np 4 ./$(RADIX4_TARGET)
	@echo ""
	@echo "SIMD Radix-4 (4进程):"
	@mpirun -np 4 ./$(SIMD_RADIX4_TARGET)

test: $(TARGET)
	@echo "运行MPI并行化NTT性能测试..."
	@for np in 1 2 4 8; do \
		echo "==================="; \
		echo "使用 $$np 个进程运行:"; \
		echo "==================="; \
		mpirun -np $$np ./$(TARGET); \
		echo ""; \
	done

test1: $(TARGET)
	mpirun -np 1 ./$(TARGET)

test2: $(TARGET)
	mpirun -np 2 ./$(TARGET)

test4: $(TARGET)
	mpirun -np 4 ./$(TARGET)

test8: $(TARGET)
	mpirun -np 8 ./$(TARGET)

# CRT优化版本的目标
CRT_OPTIMIZED_TARGET = crt_optimized_mpi
CRT_OPTIMIZED_SOURCE = main_crt_optimized_mpi.cc
CRT_SIMD_TARGET = crt_simd_mpi
CRT_SIMD_SOURCE = main_crt_simd_mpi.cc
CRT_HYBRID_TARGET = crt_hybrid_parallel
CRT_HYBRID_SOURCE = main_crt_hybrid_parallel.cc
CRT_ORIGINAL_TARGET = crt_original_mpi
CRT_ORIGINAL_SOURCE = main_crt_mpi.cc

# 新算法探索版本的目标
SPLIT_RADIX_TARGET = split_radix_simd_mpi
SPLIT_RADIX_SOURCE = main_split_radix_simd_mpi.cc
LARGE_SCALE_TARGET = large_scale_ntt
LARGE_SCALE_SOURCE = main_large_scale_ntt.cc
IMPROVED_SIX_STEP_TARGET = improved_six_step_ntt
IMPROVED_SIX_STEP_SOURCE = main_improved_six_step_ntt.cc

# 新算法版本编译
advanced: split-radix large-scale improved-six-step

split-radix: $(SPLIT_RADIX_TARGET)
$(SPLIT_RADIX_TARGET): $(SPLIT_RADIX_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(SPLIT_RADIX_TARGET) $(SPLIT_RADIX_SOURCE)

large-scale: $(LARGE_SCALE_TARGET)
$(LARGE_SCALE_TARGET): $(LARGE_SCALE_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(LARGE_SCALE_TARGET) $(LARGE_SCALE_SOURCE)

improved-six-step: $(IMPROVED_SIX_STEP_TARGET)
$(IMPROVED_SIX_STEP_TARGET): $(IMPROVED_SIX_STEP_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(IMPROVED_SIX_STEP_TARGET) $(IMPROVED_SIX_STEP_SOURCE)

# CRT版本编译
crt: crt-optimized crt-simd crt-hybrid crt-original

crt-optimized: $(CRT_OPTIMIZED_TARGET)
$(CRT_OPTIMIZED_TARGET): $(CRT_OPTIMIZED_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(CRT_OPTIMIZED_TARGET) $(CRT_OPTIMIZED_SOURCE)

crt-simd: $(CRT_SIMD_TARGET)
$(CRT_SIMD_TARGET): $(CRT_SIMD_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(CRT_SIMD_TARGET) $(CRT_SIMD_SOURCE)

crt-hybrid: $(CRT_HYBRID_TARGET)
$(CRT_HYBRID_TARGET): $(CRT_HYBRID_SOURCE)
	$(CXX) $(CXXFLAGS) -fopenmp -o $(CRT_HYBRID_TARGET) $(CRT_HYBRID_SOURCE)

crt-original: $(CRT_ORIGINAL_TARGET)
$(CRT_ORIGINAL_TARGET): $(CRT_ORIGINAL_SOURCE)
	$(CXX) $(CXXFLAGS) -o $(CRT_ORIGINAL_TARGET) $(CRT_ORIGINAL_SOURCE)

# 新算法测试目标
test-advanced: test-split-radix test-large-scale test-improved-six-step

test-split-radix: $(SPLIT_RADIX_TARGET)
	@echo "测试Split-Radix SIMD NTT实现..."
	mpirun -np 4 ./$(SPLIT_RADIX_TARGET)

test-large-scale: $(LARGE_SCALE_TARGET)
	@echo "测试大规模分布式NTT实现..."
	mpirun -np 4 ./$(LARGE_SCALE_TARGET)

test-improved-six-step: $(IMPROVED_SIX_STEP_TARGET)
	@echo "测试改进的六步法NTT实现..."
	mpirun -np 4 ./$(IMPROVED_SIX_STEP_TARGET)

# 算法性能对比测试
test-algorithm-comparison: advanced
	@echo "=================================="
	@echo "算法性能对比测试"
	@echo "=================================="
	@echo "Split-Radix NTT (4进程):"
	@mpirun -np 4 ./$(SPLIT_RADIX_TARGET)
	@echo ""
	@echo "改进的六步法NTT (4进程):"
	@mpirun -np 4 ./$(IMPROVED_SIX_STEP_TARGET)
	@echo ""
	@echo "大规模数据处理 (4进程):"
	@mpirun -np 4 ./$(LARGE_SCALE_TARGET)

# CRT测试目标
test-crt: test-crt-optimized test-crt-simd test-crt-hybrid

test-crt-optimized: $(CRT_OPTIMIZED_TARGET)
	@echo "测试优化的CRT实现..."
	mpirun -np 4 ./$(CRT_OPTIMIZED_TARGET)

test-crt-simd: $(CRT_SIMD_TARGET)
	@echo "测试SIMD优化的CRT实现..."
	mpirun -np 4 ./$(CRT_SIMD_TARGET)

test-crt-hybrid: $(CRT_HYBRID_TARGET)
	@echo "测试混合并行CRT实现..."
	mpirun -np 4 ./$(CRT_HYBRID_TARGET)

test-crt-original: $(CRT_ORIGINAL_TARGET)
	@echo "测试原始CRT实现..."
	mpirun -np 4 ./$(CRT_ORIGINAL_TARGET)

# CRT性能对比测试
test-crt-compare: crt
	@echo "=================================="
	@echo "CRT实现性能对比测试"
	@echo "=================================="
	@echo "原始CRT实现 (4进程):"
	@mpirun -np 4 ./$(CRT_ORIGINAL_TARGET)
	@echo ""
	@echo "优化CRT实现 (4进程):"
	@mpirun -np 4 ./$(CRT_OPTIMIZED_TARGET)
	@echo ""
	@echo "SIMD CRT实现 (4进程):"
	@mpirun -np 4 ./$(CRT_SIMD_TARGET)
	@echo ""
	@echo "混合并行CRT实现 (4进程):"
	@mpirun -np 4 ./$(CRT_HYBRID_TARGET)

# SIMD版本的单独测试
test-simd-radix2-4: $(SIMD_RADIX2_TARGET)
	mpirun -np 4 ./$(SIMD_RADIX2_TARGET)

test-simd-radix4-4: $(SIMD_RADIX4_TARGET)
	mpirun -np 4 ./$(SIMD_RADIX4_TARGET)

clean:
	rm -f $(TARGET) $(SIMD_RADIX2_TARGET) $(SIMD_RADIX4_TARGET) $(RADIX2_TARGET) $(RADIX4_TARGET) \
	      $(CRT_OPTIMIZED_TARGET) $(CRT_SIMD_TARGET) $(CRT_HYBRID_TARGET) $(CRT_ORIGINAL_TARGET) \
	      $(SPLIT_RADIX_TARGET) $(LARGE_SCALE_TARGET) $(IMPROVED_SIX_STEP_TARGET) \
	      files/*.out large_*.dat mega_result.dat result_*.dat

install-deps:
	@echo "检查MPI环境..."
	@which mpirun || echo "请安装MPI环境 (如 sudo apt install mpich)"
	@which mpicxx || echo "请安装MPI编译器"
	@echo "检查SIMD支持..."
	@echo "编译器将自动检测ARM NEON支持"

help:
	@echo "可用的编译目标:"
	@echo "  all          - 编译原始版本"
	@echo "  simd         - 编译所有SIMD优化版本"
	@echo "  crt          - 编译所有CRT优化版本"
	@echo "  advanced     - 编译新算法探索版本"
	@echo ""
	@echo "SIMD优化版本:"
	@echo "  simd-radix2  - 编译SIMD Radix-2版本"
	@echo "  simd-radix4  - 编译SIMD Radix-4版本"
	@echo ""
	@echo "CRT优化版本:"
	@echo "  crt-optimized- 编译优化的CRT版本"
	@echo "  crt-simd     - 编译SIMD优化的CRT版本"
	@echo "  crt-hybrid   - 编译混合并行CRT版本"
	@echo "  crt-original - 编译原始CRT版本"
	@echo ""
	@echo "新算法探索版本:"
	@echo "  split-radix  - 编译Split-Radix SIMD NTT"
	@echo "  large-scale  - 编译大规模分布式NTT"
	@echo "  improved-six-step - 编译改进的六步法NTT"
	@echo ""
	@echo "可用的测试目标:"
	@echo "  test         - 测试原始版本"
	@echo "  test-simd    - 测试所有SIMD版本"
	@echo "  test-crt     - 测试所有CRT版本"
	@echo "  test-advanced- 测试新算法版本"
	@echo "  test-algorithm-comparison - 算法性能对比"
	@echo "  test-crt-compare - CRT性能对比测试"