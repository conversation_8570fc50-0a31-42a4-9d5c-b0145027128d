CXX = mpicxx
CXXFLAGS = -O3 -std=c++17 -Wall -Wextra
TARGET = main_DIF_DIT_mpi
SOURCE = main_DIF_DIT_mpi.cc

.PHONY: all clean test test1 test2 test4 test8

all: $(TARGET)

$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE)

test: $(TARGET)
	@echo "运行MPI并行化NTT性能测试..."
	@for np in 1 2 4 8; do \
		echo "==================="; \
		echo "使用 $$np 个进程运行:"; \
		echo "==================="; \
		mpirun -np $$np ./$(TARGET); \
		echo ""; \
	done

test1: $(TARGET)
	mpirun -np 1 ./$(TARGET)

test2: $(TARGET)
	mpirun -np 2 ./$(TARGET)

test4: $(TARGET)
	mpirun -np 4 ./$(TARGET)

test8: $(TARGET)
	mpirun -np 8 ./$(TARGET)

clean:
	rm -f $(TARGET) files/*.out

install-deps:
	@echo "检查MPI环境..."
	@which mpirun || echo "请安装MPI环境 (如 sudo apt install mpich)"
	@which mpicxx || echo "请安装MPI编译器" 