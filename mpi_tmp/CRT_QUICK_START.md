# CRT多模数NTT优化版本快速开始指南

## 快速编译和运行

### 1. 编译所有CRT版本
```bash
cd mpi_tmp
make crt
```

### 2. 推荐使用（当前最佳性能）
```bash
# 编译并测试原始CRT版本（推荐）
make crt-original
make test-crt-original
```

### 3. 测试优化版本
```bash
# 测试基础优化版本
make test-crt-optimized

# 测试SIMD版本（有正确性问题，仅供参考）
make test-crt-simd

# 测试混合并行版本（有内存错误，暂不推荐）
# make test-crt-hybrid
```

### 4. 性能对比
```bash
# 运行完整性能对比（推荐）
make test-crt-compare
```

## 版本选择指南

### 🏆 推荐版本：原始CRT (`crt_original_mpi`)
- **性能**: 当前测试环境下最佳
- **稳定性**: 所有测试用例通过
- **适用场景**: 所有规模的数据
- **使用命令**: `mpirun -np 4 ./crt_original_mpi`

### ⚡ 基础优化版本 (`crt_optimized_mpi`)
- **特性**: 动态模数选择、算法优化
- **性能**: 比原始版本慢约44%（当前数据规模）
- **稳定性**: 所有测试用例通过
- **适用场景**: 可能在更大规模数据上有优势
- **使用命令**: `mpirun -np 4 ./crt_optimized_mpi`

### 🔧 SIMD版本 (`crt_simd_mpi`) - 需要修复
- **特性**: ARM NEON向量化优化
- **性能**: 大数据上有7%提升
- **问题**: 大数据测试用例结果错误
- **状态**: 需要调试和修复
- **使用命令**: `mpirun -np 4 ./crt_simd_mpi`

### ❌ 混合并行版本 (`crt_hybrid_parallel`) - 暂不可用
- **特性**: MPI+OpenMP+SIMD三层并行
- **问题**: 内存对齐错误，程序崩溃
- **状态**: 需要重大修复
- **不推荐使用**

## 性能数据摘要

### 大规模数据 (n=131072) 性能对比
| 版本 | 平均执行时间 | 相对性能 | 状态 |
|------|-------------|----------|------|
| 原始CRT | 66.6 ms | 100% (基准) | ✅ 推荐 |
| 优化CRT | 119.7 ms | 56% | ✅ 可用 |
| SIMD CRT | 62.2 ms | 107% | ❌ 有bug |
| 混合并行 | - | - | ❌ 崩溃 |

## 使用建议

### 1. 生产环境
```bash
# 使用最稳定的原始版本
mpirun -np 4 ./crt_original_mpi
```

### 2. 开发测试
```bash
# 测试不同版本的性能
make test-crt-compare
```

### 3. 研究用途
```bash
# 分析优化版本的实现
# 查看源代码：main_crt_optimized_mpi.cc
# 查看SIMD实现：main_crt_simd_mpi.cc
```

## 故障排除

### 编译问题
```bash
# 检查MPI环境
which mpirun
which mpicxx

# 检查OpenMP支持
echo | mpicxx -fopenmp -dM -E - | grep -i openmp
```

### 运行问题
```bash
# 单进程测试
mpirun -np 1 ./crt_original_mpi

# 检查测试数据
ls ../nttdata/

# 调试模式
mpirun -np 4 --debug ./crt_original_mpi
```

### 性能问题
- **小数据**: 使用原始CRT版本
- **大数据**: 可以尝试优化版本（修复后）
- **多核环境**: 调整MPI进程数

## 开发指南

### 修复SIMD版本
1. **定位问题**: CRT重构算法的SIMD实现
2. **调试方法**: 对比标量和SIMD结果
3. **测试策略**: 逐步验证每个SIMD函数

### 修复混合并行版本
1. **内存对齐**: 检查SIMD数据的内存对齐
2. **线程安全**: 确保OpenMP和MPI的正确交互
3. **简化设计**: 移除不必要的复杂性

### 性能优化方向
1. **自适应策略**: 根据数据规模选择算法
2. **硬件适配**: 针对特定硬件优化
3. **算法改进**: 探索更高效的CRT算法

## 文档参考

- **详细技术报告**: `CRT_OPTIMIZATION_REPORT.md`
- **性能对比分析**: `CRT_PERFORMANCE_COMPARISON.md`
- **源代码注释**: 查看各个.cc文件的详细注释

## 联系和支持

如有问题或建议：
1. 查看源代码中的详细注释
2. 参考技术报告和性能分析
3. 使用调试工具分析具体问题

---

**重要提醒**: 
- 当前推荐使用原始CRT版本 (`crt_original_mpi`)
- 优化版本仍在完善中，主要用于研究和学习
- 在生产环境中请优先考虑稳定性和正确性
