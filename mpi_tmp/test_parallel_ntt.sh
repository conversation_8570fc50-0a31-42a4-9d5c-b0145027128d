#!/bin/bash

# 并行NTT测试脚本
# 测试不同进程数下的性能和正确性

echo "========================================"
echo "并行Barrett NTT测试脚本"
echo "========================================"

# 检查可执行文件是否存在
if [ ! -f "./ntt_parallel" ]; then
    echo "错误: ntt_parallel 可执行文件不存在"
    echo "请先编译: mpic++ -O3 -std=c++20 -march=native main_barrett_radix2_ntt.cc -o ntt_parallel"
    exit 1
fi

# 检查测试数据是否存在
if [ ! -d "../nttdata" ]; then
    echo "错误: 测试数据目录 ../nttdata 不存在"
    exit 1
fi

echo "开始测试..."
echo

# 测试不同进程数
for np in 1 2 4 8; do
    echo "----------------------------------------"
    echo "测试 $np 进程:"
    echo "----------------------------------------"
    
    # 运行测试
    mpirun -np $np ./ntt_parallel
    
    if [ $? -eq 0 ]; then
        echo "✅ $np 进程测试成功"
    else
        echo "❌ $np 进程测试失败"
    fi
    echo
done

echo "========================================"
echo "测试完成"
echo "========================================"

# 检查输出文件
echo "检查输出文件:"
for i in 0 1 2 3; do
    if [ -f "files/$i.out" ]; then
        echo "✅ files/$i.out 已生成"
    else
        echo "❌ files/$i.out 未生成"
    fi
done

echo
echo "如需详细性能分析，请运行:"
echo "python3 performance_analysis.py"
