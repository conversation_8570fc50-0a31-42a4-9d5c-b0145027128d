import matplotlib.pyplot as plt
import numpy as np

params = ["n=4, p=7340033", "n=131072, p=7340033", "n=131072, p=104857601", "n=131072, p=469762049"]
serial_times = [62.94, 90756.2, 91355.8, 94774.2]
np2_times = [55.07, 67952.6, 68166, 69733.6]
np4_times = [56.74, 57372.8, 56359.3, 57238.1]
np8_times = [81.78, 75302.4, 58904.9, 87534.4]

np2_speedup = np.array(serial_times) / np.array(np2_times)
np4_speedup = np.array(serial_times) / np.array(np4_times)
np8_speedup = np.array(serial_times) / np.array(np8_times)

fig, ax = plt.subplots(1, 2, figsize=(14, 6))

ax[0].barh(params, serial_times, label='Serial NTT', color='lightblue', edgecolor='black')
ax[0].barh(params, np2_times, label='np=2', color='lightgreen', edgecolor='black')
ax[0].barh(params, np4_times, label='np=4', color='lightcoral', edgecolor='black')
ax[0].barh(params, np8_times, label='np=8', color='yellow', edgecolor='black')

ax[0].set_xlabel('Time (ms)')
ax[0].set_title('Serial vs Parallel NTT Times')
ax[0].legend()

ax[1].bar(params, np2_speedup, label='np=2', width=0.2, align='center', color='lightgreen', edgecolor='black')
ax[1].bar(params, np4_speedup, label='np=4', width=0.2, align='edge', color='lightcoral', edgecolor='black')
ax[1].bar(params, np8_speedup, label='np=8', width=0.2, align='edge', color='yellow', edgecolor='black')

ax[1].set_xlabel('Parameters (n, p)')
ax[1].set_ylabel('Speedup')
ax[1].set_title('Speedup Comparison')
ax[1].legend()

plt.tight_layout()
plt.savefig('ntt_comparison.png')
plt.show()
