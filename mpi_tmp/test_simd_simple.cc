/****************************************************************************************
 * test_simd_simple.cc - 简化的SIMD测试版本
 * 用于验证基本的SIMD功能和MPI通信
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

using namespace std;

/* ============================== I/O 函数 ============================== */
void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输入文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输出文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; 
        fin >> x;
        if (x != ab[i]) { 
            cout << "多项式乘法结果错误 (id="<<input_id<<")\n"; 
            return; 
        }
    }
    cout << "多项式乘法结果正确 (id="<<input_id<<")\n";
}

/* ============================== 简化的Barrett模运算 ============================== */
class SimpleBarrett {
public:
    explicit SimpleBarrett(unsigned int m): mod(m) {}
    
    inline unsigned int mul(unsigned int a, unsigned int b) const {
        return (1ULL * a * b) % mod;
    }
    
    inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int s = a + b;
        return s >= mod ? s - mod : s;
    }
    
    inline unsigned int sub(unsigned int a, unsigned int b) const {
        return a >= b ? a - b : a + mod - b;
    }
    
    const unsigned int mod;
};

/* ============================== 工具函数 ============================== */
static unsigned int mod_pow(unsigned int a, unsigned long long e, unsigned int mod) {
    unsigned long long res = 1, base = a;
    while (e) {
        if (e & 1) res = res * base % mod;
        base = base * base % mod;
        e >>= 1;
    }
    return static_cast<unsigned int>(res);
}

static void bit_reverse(vector<unsigned int>& a) {
    int n = a.size();
    int lg = __builtin_ctz(n);
    for (int i = 0; i < n; ++i) {
        int rev = 0;
        for (int j = 0; j < lg; ++j) {
            if (i >> j & 1) rev |= 1 << (lg - 1 - j);
        }
        if (i < rev) swap(a[i], a[rev]);
    }
}

/* ============================== 简化的NTT实现 ============================== */
void simple_ntt(vector<unsigned int>& a, bool inverse, const SimpleBarrett& br, unsigned int g = 3) {
    int n = a.size();
    
    bit_reverse(a);
    
    for (int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        unsigned int wn = mod_pow(g, (br.mod - 1) / len, br.mod);
        if (inverse) {
            wn = mod_pow(wn, br.mod - 2, br.mod);
        }
        
        for (int i = 0; i < n; i += len) {
            unsigned int w = 1;
            for (int j = 0; j < m; ++j) {
                unsigned int u = a[i + j];
                unsigned int v = br.mul(a[i + j + m], w);
                a[i + j] = br.add(u, v);
                a[i + j + m] = br.sub(u, v);
                w = br.mul(w, wn);
            }
        }
    }
    
    if (inverse) {
        unsigned int inv_n = mod_pow(n, br.mod - 2, br.mod);
        for (unsigned int &x : a) {
            x = br.mul(x, inv_n);
        }
    }
}

/* ============================== 简化的多项式乘法 ============================== */
void simple_poly_multiply(const int* a, const int* b, int* ab, int n, int p, int rank) {
    SimpleBarrett br(p);
    int lim = 1;
    while(lim < 2*n) lim <<= 1;

    vector<unsigned int> A(lim, 0), B(lim, 0);

    // 只在rank 0进行数据初始化
    if(rank == 0) {
        for(int i = 0; i < n; ++i) {
            A[i] = ((a[i] % p) + p) % p;
            B[i] = ((b[i] % p) + p) % p;
        }
    }

    // 广播初始数据到所有进程
    MPI_Bcast(A.data(), lim, MPI_UNSIGNED, 0, MPI_COMM_WORLD);
    MPI_Bcast(B.data(), lim, MPI_UNSIGNED, 0, MPI_COMM_WORLD);

    // NTT正变换
    simple_ntt(A, false, br);
    simple_ntt(B, false, br);

    // 点乘
    for(int i = 0; i < lim; ++i) {
        A[i] = br.mul(A[i], B[i]);
    }

    // NTT逆变换
    simple_ntt(A, true, br);

    // 只在rank 0复制结果
    if(rank == 0) {
        for(int i = 0; i < 2*n-1; ++i) {
            ab[i] = static_cast<int>(A[i]);
        }
    }
}

/* ============================== 主函数 ============================== */
int main(int argc, char* argv[]) {
    MPI_Init(&argc, &argv);

    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    if(rank == 0) {
        cout << "简化SIMD测试版本, ranks = " << size << '\n';
#ifdef __ARM_NEON
        cout << "SIMD优化: ARM NEON 已启用\n";
#else
        cout << "SIMD优化: 未启用\n";
#endif
        cout.flush();
    }

    // 测试数据
    static int a[300000], b[300000], ab[600000];
    
    for(int id = 0; id <= 3; ++id) {
        int n, p;

        // 只有rank 0读取数据
        if(rank == 0) {
            fRead(a, b, &n, &p, id);
        }

        // 广播n和p到所有进程
        MPI_Bcast(&n, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(&p, 1, MPI_INT, 0, MPI_COMM_WORLD);

        // 广播输入数据到所有进程
        MPI_Bcast(a, n, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(b, n, MPI_INT, 0, MPI_COMM_WORLD);

        MPI_Barrier(MPI_COMM_WORLD);

        // 简化的多项式乘法
        auto t0 = chrono::high_resolution_clock::now();
        simple_poly_multiply(a, b, ab, n, p, rank);
        MPI_Barrier(MPI_COMM_WORLD);
        auto t1 = chrono::high_resolution_clock::now();
        double us = chrono::duration<double, std::micro>(t1 - t0).count();

        // 只在rank 0进行验证
        if(rank == 0) {
            fCheck(ab, n, id);
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p << "): " 
                 << fixed << setprecision(2) << us << " us\n";
            cout.flush();
        }
    }

    MPI_Finalize();
    return 0;
}
